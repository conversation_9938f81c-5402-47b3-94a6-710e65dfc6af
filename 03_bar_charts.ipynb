{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# 柱状图（条形图）- 从入门到精通\n", "\n", "## 📖 本节学习目标\n", "- 掌握柱状图的基本概念和适用场景\n", "- 学会使用matplotlib创建各种类型的柱状图\n", "- 理解垂直、水平、分组、堆叠柱状图的区别和用法\n", "- 能够根据数据特点选择合适的柱状图类型\n", "- 掌握柱状图的美化和定制技巧"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 导入必要的库\n", "import matplotlib.pyplot as plt\n", "import numpy as np\n", "import pandas as pd\n", "from matplotlib.patches import Rectangle\n", "\n", "# 设置中文字体\n", "plt.rcParams['font.sans-serif'] = ['SimHei', 'DejaVu Sans']\n", "plt.rcParams['axes.unicode_minus'] = False\n", "\n", "# 设置图表样式\n", "plt.style.use('default')\n", "\n", "print(\"✅ 环境配置完成！\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 🎯 知识点详解\n", "\n", "### 1. 柱状图概念介绍\n", "\n", "**定义和特点**：\n", "- 柱状图使用矩形条的高度或长度来表示数据的大小\n", "- 是展示分类数据最常用的图表类型\n", "- 便于比较不同类别之间的数值差异\n", "- 可以清晰显示数据的排序和相对大小关系\n", "\n", "**适用数据类型**：\n", "- 分类变量（x轴）+ 数值变量（y轴）\n", "- 离散的类别数据，如产品销量、地区人口、考试成绩分布等\n", "- 时间序列数据（当时间点较少时）\n", "\n", "**使用场景分析**：\n", "- 比较不同类别的数值大小\n", "- 展示数据的排名和分布\n", "- 显示数据随时间的变化（离散时间点）\n", "- 展示部分与整体的关系（堆叠柱状图）\n", "\n", "**与其他图表的区别**：\n", "- 与散点图：柱状图用于分类数据，散点图用于连续数据\n", "- 与线图：柱状图强调数值大小，线图强调趋势变化\n", "- 与饼图：柱状图便于精确比较，饼图强调比例关系"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 2. 基础语法和参数\n", "\n", "**核心函数介绍**：\n", "- `plt.bar(x, height, ...)` - 创建垂直柱状图\n", "- `plt.barh(y, width, ...)` - 创建水平柱状图\n", "- `ax.bar()` / `ax.barh()` - 面向对象接口\n", "\n", "**必需参数详解**：\n", "- `x` (bar) / `y` (barh): 柱子的位置，可以是数值或字符串列表\n", "- `height` (bar) / `width` (barh): 柱子的高度或宽度，必须是数值\n", "\n", "**重要可选参数说明**：\n", "- `width` (bar) / `height` (barh): 柱子的宽度，默认0.8\n", "- `color`: 柱子的颜色，可以是单一颜色或颜色列表\n", "- `alpha`: 透明度，0-1之间的浮点数\n", "- `edgecolor`: 柱子边框颜色\n", "- `linewidth`: 边框宽度\n", "- `align`: 对齐方式，'center'（默认）或'edge'\n", "- `label`: 图例标签\n", "\n", "**返回值说明**：\n", "- 返回BarContainer对象，包含所有柱子的Rectangle对象，可用于后续修改"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 3. 代码演示（从简单到复杂）\n", "\n", "#### 3.1 最简单示例"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 创建基础垂直柱状图\n", "# 示例数据：不同城市的人口数量\n", "cities = ['北京', '上海', '广州', '深圳', '杭州']\n", "population = [2154, 2424, 1491, 1344, 1196]  # 单位：万人\n", "\n", "# 创建最简单的柱状图\n", "plt.figure(figsize=(10, 6))\n", "plt.bar(cities, population)  # 使用默认参数创建柱状图\n", "plt.title('主要城市人口数量')  # 添加标题\n", "plt.xlabel('城市')  # x轴标签\n", "plt.ylabel('人口数量（万人）')  # y轴标签\n", "plt.grid(True, alpha=0.3, axis='y')  # 添加水平网格线\n", "plt.show()\n", "\n", "print(\"✅ 基础柱状图创建完成！\")\n", "print(f\"城市数量: {len(cities)}\")\n", "print(f\"人口范围: {min(population)} - {max(population)} 万人\")\n", "print(f\"平均人口: {np.mean(population):.0f} 万人\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 创建基础水平柱状图\n", "plt.figure(figsize=(10, 6))\n", "plt.barh(cities, population, color='lightcoral')  # 水平柱状图\n", "plt.title('主要城市人口数量（水平版）')\n", "plt.xlabel('人口数量（万人）')\n", "plt.ylabel('城市')\n", "plt.grid(True, alpha=0.3, axis='x')  # 添加垂直网格线\n", "\n", "# 在柱子上添加数值标签\n", "for i, v in enumerate(population):\n", "    plt.text(v + 20, i, str(v), va='center', fontweight='bold')\n", "\n", "plt.tight_layout()\n", "plt.show()\n", "\n", "print(\"📊 水平柱状图的优势：\")\n", "print(\"- 类别名称较长时更易阅读\")\n", "print(\"- 便于添加数值标签\")\n", "print(\"- 适合展示排名数据\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### 3.2 参数定制示例"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 展示各种参数的效果\n", "fig, axes = plt.subplots(2, 2, figsize=(15, 10))\n", "fig.suptitle('柱状图参数定制示例', fontsize=16, fontweight='bold')\n", "\n", "# 示例数据\n", "categories = ['产品A', '产品B', '产品C', '产品D', '产品E']\n", "values = [23, 45, 56, 78, 32]\n", "\n", "# 子图1：颜色和透明度\n", "colors = ['#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7']\n", "bars1 = axes[0, 0].bar(categories, values, color=colors, alpha=0.8)\n", "axes[0, 0].set_title('自定义颜色和透明度')\n", "axes[0, 0].set_ylabel('数值')\n", "axes[0, 0].grid(True, alpha=0.3, axis='y')\n", "\n", "# 子图2：边框设置\n", "bars2 = axes[0, 1].bar(categories, values, color='lightblue', \n", "                      edgecolor='darkblue', linewidth=2)\n", "axes[0, 1].set_title('边框设置')\n", "axes[0, 1].set_ylabel('数值')\n", "axes[0, 1].grid(True, alpha=0.3, axis='y')\n", "\n", "# 子图3：柱子宽度调整\n", "bars3 = axes[1, 0].bar(categories, values, width=0.5, color='orange')\n", "axes[1, 0].set_title('调整柱子宽度 (width=0.5)')\n", "axes[1, 0].set_ylabel('数值')\n", "axes[1, 0].grid(True, alpha=0.3, axis='y')\n", "\n", "# 子图4：添加数值标签和美化\n", "bars4 = axes[1, 1].bar(categories, values, color='steelblue', alpha=0.7)\n", "axes[1, 1].set_title('添加数值标签')\n", "axes[1, 1].set_ylabel('数值')\n", "axes[1, 1].grid(True, alpha=0.3, axis='y')\n", "\n", "# 在柱子顶部添加数值标签\n", "for bar, value in zip(bars4, values):\n", "    height = bar.get_height()\n", "    axes[1, 1].text(bar.get_x() + bar.get_width()/2., height + 1,\n", "                    f'{value}', ha='center', va='bottom', fontweight='bold')\n", "\n", "# 旋转x轴标签\n", "for ax in axes.flat:\n", "    ax.tick_params(axis='x', rotation=45)\n", "\n", "plt.tight_layout()\n", "plt.show()\n", "\n", "print(\"🎨 参数定制技巧：\")\n", "print(\"- 使用颜色列表为每个柱子设置不同颜色\")\n", "print(\"- edgecolor和linewidth控制边框样式\")\n", "print(\"- width参数调整柱子宽度（0-1之间）\")\n", "print(\"- 通过bar对象的属性添加标签\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### 3.3 美化和进阶示例"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 分组柱状图 - 比较多个系列的数据\n", "fig, axes = plt.subplots(1, 2, figsize=(16, 6))\n", "fig.suptitle('高级柱状图技巧', fontsize=16, fontweight='bold')\n", "\n", "# 数据准备\n", "quarters = ['Q1', 'Q2', 'Q3', 'Q4']\n", "product_a = [20, 35, 30, 25]\n", "product_b = [25, 30, 35, 30]\n", "product_c = [15, 25, 20, 35]\n", "\n", "# 子图1：分组柱状图\n", "x = np.arange(len(quarters))  # 标签位置\n", "width = 0.25  # 柱子宽度\n", "\n", "bars1 = axes[0].bar(x - width, product_a, width, label='产品A', \n", "                   color='#FF6B6B', alpha=0.8)\n", "bars2 = axes[0].bar(x, product_b, width, label='产品B', \n", "                   color='#4ECDC4', alpha=0.8)\n", "bars3 = axes[0].bar(x + width, product_c, width, label='产品C', \n", "                   color='#45B7D1', alpha=0.8)\n", "\n", "axes[0].set_title('分组柱状图 - 季度销售对比')\n", "axes[0].set_xlabel('季度')\n", "axes[0].set_ylabel('销售额（万元）')\n", "axes[0].set_xticks(x)\n", "axes[0].set_xticklabels(quarters)\n", "axes[0].legend()\n", "axes[0].grid(True, alpha=0.3, axis='y')\n", "\n", "# 为分组柱状图添加数值标签\n", "def add_value_labels(ax, bars):\n", "    for bar in bars:\n", "        height = bar.get_height()\n", "        ax.text(bar.get_x() + bar.get_width()/2., height + 0.5,\n", "               f'{height}', ha='center', va='bottom', fontsize=9)\n", "\n", "add_value_labels(axes[0], bars1)\n", "add_value_labels(axes[0], bars2)\n", "add_value_labels(axes[0], bars3)\n", "\n", "# 子图2：堆叠柱状图\n", "bars1_stack = axes[1].bar(quarters, product_a, label='产品A', \n", "                         color='#FF6B6B', alpha=0.8)\n", "bars2_stack = axes[1].bar(quarters, product_b, bottom=product_a, \n", "                         label='产品B', color='#4ECDC4', alpha=0.8)\n", "bars3_stack = axes[1].bar(quarters, product_c, \n", "                         bottom=np.array(product_a) + np.array(product_b), \n", "                         label='产品C', color='#45B7D1', alpha=0.8)\n", "\n", "axes[1].set_title('堆叠柱状图 - 季度销售构成')\n", "axes[1].set_xlabel('季度')\n", "axes[1].set_ylabel('销售额（万元）')\n", "axes[1].legend()\n", "axes[1].grid(True, alpha=0.3, axis='y')\n", "\n", "plt.tight_layout()\n", "plt.show()\n", "\n", "print(\"📊 高级柱状图类型：\")\n", "print(\"- 分组柱状图：适合比较多个系列在不同类别下的表现\")\n", "print(\"- 堆叠柱状图：适合展示部分与整体的关系\")\n", "print(\"- 分组图需要调整x位置和柱子宽度\")\n", "print(\"- 堆叠图需要设置bottom参数\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 专业级柱状图 - 带排序和百分比标签\n", "# 模拟数据：不同部门的预算分配\n", "departments = ['研发部', '销售部', '市场部', '人事部', '财务部', '运营部']\n", "budgets = [450, 320, 280, 150, 120, 200]\n", "\n", "# 按预算大小排序\n", "sorted_data = sorted(zip(departments, budgets), key=lambda x: x[1], reverse=True)\n", "sorted_departments, sorted_budgets = zip(*sorted_data)\n", "\n", "# 计算百分比\n", "total_budget = sum(sorted_budgets)\n", "percentages = [budget/total_budget*100 for budget in sorted_budgets]\n", "\n", "plt.figure(figsize=(14, 8))\n", "\n", "# 创建渐变色\n", "colors = plt.cm.viridis(np.linspace(0, 1, len(sorted_departments)))\n", "\n", "bars = plt.bar(sorted_departments, sorted_budgets, color=colors, \n", "              alpha=0.8, edgecolor='white', linewidth=2)\n", "\n", "# 添加数值和百分比标签\n", "for bar, budget, pct in zip(bars, sorted_budgets, percentages):\n", "    height = bar.get_height()\n", "    plt.text(bar.get_x() + bar.get_width()/2., height + 5,\n", "            f'{budget}万\\n({pct:.1f}%)', \n", "            ha='center', va='bottom', fontweight='bold', fontsize=10)\n", "\n", "plt.title('部门预算分配情况', fontsize=16, fontweight='bold', pad=20)\n", "plt.xlabel('部门', fontsize=12)\n", "plt.ylabel('预算金额（万元）', fontsize=12)\n", "plt.grid(True, alpha=0.3, axis='y', linestyle='--')\n", "\n", "# 添加总预算信息\n", "plt.text(0.02, 0.98, f'总预算: {total_budget}万元', \n", "         transform=plt.gca().transAxes, fontsize=12, fontweight='bold',\n", "         bbox=dict(boxstyle='round', facecolor='wheat', alpha=0.8),\n", "         verticalalignment='top')\n", "\n", "# 旋转x轴标签\n", "plt.xticks(rotation=45, ha='right')\n", "plt.tight_layout()\n", "plt.show()\n", "\n", "print(\"🌟 专业柱状图特点：\")\n", "print(f\"- 按数值大小排序，便于比较\")\n", "print(f\"- 使用渐变色增强视觉效果\")\n", "print(f\"- 同时显示绝对值和百分比\")\n", "print(f\"- 添加总计信息提供上下文\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 🔍 举一反三\n", "\n", "### 场景1：电商销售分析 - 多维度销售数据对比\n", "**问题描述**：电商平台需要分析不同类目商品在各个月份的销售表现，同时对比线上线下渠道的差异\n", "**解决思路**：使用分组柱状图展示多个维度的对比，通过颜色区分渠道，通过位置区分月份\n", "**代码实现**："]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 电商销售分析\n", "import matplotlib.pyplot as plt\n", "import numpy as np\n", "\n", "# 模拟电商销售数据\n", "months = ['1月', '2月', '3月', '4月', '5月', '6月']\n", "categories = ['服装', '电子', '家居', '美妆']\n", "\n", "# 线上销售数据（万元）\n", "online_sales = {\n", "    '服装': [120, 150, 180, 200, 220, 250],\n", "    '电子': [200, 180, 220, 240, 260, 280],\n", "    '家居': [80, 90, 100, 120, 140, 160],\n", "    '美妆': [60, 70, 85, 95, 110, 125]\n", "}\n", "\n", "# 线下销售数据（万元）\n", "offline_sales = {\n", "    '服装': [80, 85, 90, 95, 100, 105],\n", "    '电子': [150, 140, 160, 170, 180, 190],\n", "    '家居': [100, 110, 115, 125, 130, 135],\n", "    '美妆': [40, 45, 50, 55, 60, 65]\n", "}\n", "\n", "# 创建子图\n", "fig, axes = plt.subplots(2, 2, figsize=(16, 12))\n", "fig.suptitle('电商平台销售分析 - 多维度对比', fontsize=16, fontweight='bold')\n", "\n", "colors = ['#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4']\n", "\n", "# 为每个类目创建一个子图\n", "for idx, category in enumerate(categories):\n", "    row = idx // 2\n", "    col = idx % 2\n", "    ax = axes[row, col]\n", "    \n", "    x = np.arange(len(months))\n", "    width = 0.35\n", "    \n", "    # 绘制线上和线下销售对比\n", "    bars1 = ax.bar(x - width/2, online_sales[category], width, \n", "                  label='线上', color=colors[idx], alpha=0.8)\n", "    bars2 = ax.bar(x + width/2, offline_sales[category], width, \n", "                  label='线下', color=colors[idx], alpha=0.5)\n", "    \n", "    ax.set_title(f'{category}类目销售对比', fontweight='bold')\n", "    ax.set_xlabel('月份')\n", "    ax.set_ylabel('销售额（万元）')\n", "    ax.set_xticks(x)\n", "    ax.set_xticklabels(months)\n", "    ax.legend()\n", "    ax.grid(True, alpha=0.3, axis='y')\n", "    \n", "    # 添加增长趋势线\n", "    online_trend = np.polyfit(range(len(months)), online_sales[category], 1)\n", "    offline_trend = np.polyfit(range(len(months)), offline_sales[category], 1)\n", "    \n", "    ax.plot(x, np.polyval(online_trend, range(len(months))), \n", "           '--', color=colors[idx], alpha=0.7, linewidth=2)\n", "    ax.plot(x, np.polyval(offline_trend, range(len(months))), \n", "           ':', color=colors[idx], alpha=0.7, linewidth=2)\n", "\n", "plt.tight_layout()\n", "plt.show()\n", "\n", "# 计算并显示关键指标\n", "print(\"📊 销售分析结果：\")\n", "for category in categories:\n", "    online_total = sum(online_sales[category])\n", "    offline_total = sum(offline_sales[category])\n", "    online_growth = (online_sales[category][-1] - online_sales[category][0]) / online_sales[category][0] * 100\n", "    print(f\"{category}: 线上总销售{online_total}万元，线下{offline_total}万元，线上增长{online_growth:.1f}%\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["**知识扩展**：\n", "- 多子图布局：使用subplot创建网格布局，便于对比分析\n", "- 趋势线添加：使用numpy.polyfit拟合线性趋势\n", "- 数据聚合：计算总计和增长率等关键指标\n", "- 视觉层次：通过透明度区分主次信息"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 场景2：企业财务报表 - 收入支出结构分析\n", "**问题描述**：财务部门需要制作年度财务报表，展示各季度的收入构成和支出分布，便于董事会决策\n", "**解决思路**：使用堆叠柱状图展示收入构成，用分组柱状图对比收入支出，添加盈亏平衡线\n", "**代码实现**："]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 企业财务报表分析\n", "quarters = ['Q1', 'Q2', 'Q3', 'Q4']\n", "\n", "# 收入构成数据（万元）\n", "product_revenue = [800, 950, 1100, 1200]  # 产品销售收入\n", "service_revenue = [200, 250, 300, 350]    # 服务收入\n", "other_revenue = [50, 80, 100, 120]        # 其他收入\n", "\n", "# 支出数据（万元）\n", "operating_expense = [600, 700, 800, 850]  # 运营支出\n", "marketing_expense = [150, 200, 250, 300]  # 市场支出\n", "rd_expense = [200, 220, 240, 260]         # 研发支出\n", "\n", "# 计算总收入和总支出\n", "total_revenue = np.array(product_revenue) + np.array(service_revenue) + np.array(other_revenue)\n", "total_expense = np.array(operating_expense) + np.array(marketing_expense) + np.array(rd_expense)\n", "net_profit = total_revenue - total_expense\n", "\n", "fig, axes = plt.subplots(1, 3, figsize=(18, 6))\n", "fig.suptitle('年度财务报表分析', fontsize=16, fontweight='bold')\n", "\n", "# 子图1：收入构成堆叠柱状图\n", "axes[0].bar(quarters, product_revenue, label='产品销售', \n", "           color='#2E86AB', alpha=0.8)\n", "axes[0].bar(quarters, service_revenue, bottom=product_revenue, \n", "           label='服务收入', color='#A23B72', alpha=0.8)\n", "axes[0].bar(quarters, other_revenue, \n", "           bottom=np.array(product_revenue) + np.array(service_revenue),\n", "           label='其他收入', color='#F18F01', alpha=0.8)\n", "\n", "axes[0].set_title('季度收入构成', fontweight='bold')\n", "axes[0].set_ylabel('金额（万元）')\n", "axes[0].legend()\n", "axes[0].grid(True, alpha=0.3, axis='y')\n", "\n", "# 添加总收入标签\n", "for i, (quarter, total) in enumerate(zip(quarters, total_revenue)):\n", "    axes[0].text(i, total + 20, f'{total}万', ha='center', va='bottom', \n", "                fontweight='bold', fontsize=10)\n", "\n", "# 子图2：支出构成堆叠柱状图\n", "axes[1].bar(quarters, operating_expense, label='运营支出', \n", "           color='#FF6B6B', alpha=0.8)\n", "axes[1].bar(quarters, marketing_expense, bottom=operating_expense, \n", "           label='市场支出', color='#4ECDC4', alpha=0.8)\n", "axes[1].bar(quarters, rd_expense, \n", "           bottom=np.array(operating_expense) + np.array(marketing_expense),\n", "           label='研发支出', color='#45B7D1', alpha=0.8)\n", "\n", "axes[1].set_title('季度支出构成', fontweight='bold')\n", "axes[1].set_ylabel('金额（万元）')\n", "axes[1].legend()\n", "axes[1].grid(True, alpha=0.3, axis='y')\n", "\n", "# 添加总支出标签\n", "for i, (quarter, total) in enumerate(zip(quarters, total_expense)):\n", "    axes[1].text(i, total + 20, f'{total}万', ha='center', va='bottom', \n", "                fontweight='bold', fontsize=10)\n", "\n", "# 子图3：收支对比和净利润\n", "x = np.arange(len(quarters))\n", "width = 0.35\n", "\n", "bars1 = axes[2].bar(x - width/2, total_revenue, width, label='总收入', \n", "                   color='green', alpha=0.7)\n", "bars2 = axes[2].bar(x + width/2, total_expense, width, label='总支出', \n", "                   color='red', alpha=0.7)\n", "\n", "# 添加净利润线\n", "axes[2].plot(x, net_profit, 'o-', color='blue', linewidth=3, \n", "            markersize=8, label='净利润')\n", "\n", "# 添加盈亏平衡线\n", "axes[2].axhline(y=0, color='black', linestyle='--', alpha=0.5, label='盈亏平衡线')\n", "\n", "axes[2].set_title('收支对比与净利润', fontweight='bold')\n", "axes[2].set_ylabel('金额（万元）')\n", "axes[2].set_xticks(x)\n", "axes[2].set_xticklabels(quarters)\n", "axes[2].legend()\n", "axes[2].grid(True, alpha=0.3, axis='y')\n", "\n", "plt.tight_layout()\n", "plt.show()\n", "\n", "# 财务指标分析\n", "print(\"💰 财务分析结果：\")\n", "print(f\"年度总收入: {sum(total_revenue):.0f}万元\")\n", "print(f\"年度总支出: {sum(total_expense):.0f}万元\")\n", "print(f\"年度净利润: {sum(net_profit):.0f}万元\")\n", "print(f\"平均利润率: {sum(net_profit)/sum(total_revenue)*100:.1f}%\")\n", "print(f\"收入增长率: {(total_revenue[-1]-total_revenue[0])/total_revenue[0]*100:.1f}%\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["**知识扩展**：\n", "- 财务可视化：堆叠图展示构成，分组图展示对比，折线图展示趋势\n", "- 参考线添加：使用axhline()添加盈亏平衡线等重要参考\n", "- 混合图表：在同一图中结合柱状图和折线图\n", "- 关键指标计算：自动计算并显示重要的财务比率"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 场景3：学生成绩统计 - 多班级多科目成绩分析\n", "**问题描述**：学校需要分析各班级在不同科目上的平均成绩，识别优势学科和薄弱环节，为教学改进提供依据\n", "**解决思路**：使用热力图风格的柱状图，通过颜色深浅表示成绩高低，添加及格线和目标线\n", "**代码实现**："]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 学生成绩统计分析\n", "classes = ['一班', '二班', '三班', '四班', '五班']\n", "subjects = ['语文', '数学', '英语', '物理', '化学']\n", "\n", "# 模拟各班各科平均成绩\n", "np.random.seed(42)\n", "scores = {\n", "    '语文': [85, 82, 88, 79, 86],\n", "    '数学': [78, 85, 82, 88, 80],\n", "    '英语': [82, 79, 85, 83, 87],\n", "    '物理': [75, 88, 80, 85, 78],\n", "    '化学': [80, 83, 78, 82, 85]\n", "}\n", "\n", "fig, axes = plt.subplots(2, 3, figsize=(18, 12))\n", "fig.suptitle('各班级各科目成绩分析', fontsize=16, fontweight='bold')\n", "\n", "# 为每个科目创建一个子图\n", "for idx, subject in enumerate(subjects):\n", "    row = idx // 3\n", "    col = idx % 3\n", "    ax = axes[row, col]\n", "    \n", "    subject_scores = scores[subject]\n", "    \n", "    # 根据成绩高低设置颜色\n", "    colors = []\n", "    for score in subject_scores:\n", "        if score >= 85:\n", "            colors.append('#2E8B57')  # 优秀-深绿\n", "        elif score >= 75:\n", "            colors.append('#32CD32')  # 良好-浅绿\n", "        elif score >= 60:\n", "            colors.append('#FFD700')  # 及格-黄色\n", "        else:\n", "            colors.append('#FF6347')  # 不及格-红色\n", "    \n", "    bars = ax.bar(classes, subject_scores, color=colors, alpha=0.8, \n", "                 edgecolor='white', linewidth=2)\n", "    \n", "    # 添加及格线和优秀线\n", "    ax.axhline(y=60, color='red', linestyle='--', alpha=0.7, label='及格线')\n", "    ax.axhline(y=85, color='green', linestyle='--', alpha=0.7, label='优秀线')\n", "    \n", "    ax.set_title(f'{subject}成绩分布', fontweight='bold')\n", "    ax.set_ylabel('平均成绩')\n", "    ax.set_ylim(50, 100)\n", "    ax.grid(True, alpha=0.3, axis='y')\n", "    \n", "    # 添加成绩标签\n", "    for bar, score in zip(bars, subject_scores):\n", "        height = bar.get_height()\n", "        ax.text(bar.get_x() + bar.get_width()/2., height + 1,\n", "               f'{score}', ha='center', va='bottom', fontweight='bold')\n", "    \n", "    # 只在第一个子图显示图例\n", "    if idx == 0:\n", "        ax.legend(loc='upper right')\n", "\n", "# 最后一个位置显示综合分析\n", "ax_summary = axes[1, 2]\n", "\n", "# 计算各班总平均分\n", "class_averages = []\n", "for i in range(len(classes)):\n", "    total = sum(scores[subject][i] for subject in subjects)\n", "    average = total / len(subjects)\n", "    class_averages.append(average)\n", "\n", "# 绘制班级综合排名\n", "sorted_data = sorted(zip(classes, class_averages), key=lambda x: x[1], reverse=True)\n", "sorted_classes, sorted_averages = zip(*sorted_data)\n", "\n", "colors_rank = plt.cm.RdYlGn(np.linspace(0.3, 0.9, len(sorted_classes)))\n", "bars_summary = ax_summary.bar(sorted_classes, sorted_averages, \n", "                             color=colors_rank, alpha=0.8, \n", "                             edgecolor='white', linewidth=2)\n", "\n", "ax_summary.set_title('班级综合排名', fontweight='bold')\n", "ax_summary.set_ylabel('综合平均分')\n", "ax_summary.set_ylim(70, 90)\n", "ax_summary.grid(True, alpha=0.3, axis='y')\n", "\n", "# 添加排名标签\n", "for i, (bar, score) in enumerate(zip(bars_summary, sorted_averages)):\n", "    height = bar.get_height()\n", "    ax_summary.text(bar.get_x() + bar.get_width()/2., height + 0.5,\n", "                   f'第{i+1}名\\n{score:.1f}分', ha='center', va='bottom', \n", "                   fontweight='bold', fontsize=9)\n", "\n", "plt.tight_layout()\n", "plt.show()\n", "\n", "# 成绩分析报告\n", "print(\"📊 成绩分析报告：\")\n", "print(\"\\n各科目平均分：\")\n", "for subject in subjects:\n", "    avg_score = np.mean(scores[subject])\n", "    print(f\"{subject}: {avg_score:.1f}分\")\n", "\n", "print(\"\\n班级综合排名：\")\n", "for i, (class_name, avg) in enumerate(sorted_data):\n", "    print(f\"第{i+1}名: {class_name} ({avg:.1f}分)\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["**知识扩展**：\n", "- 条件着色：根据数值范围自动选择颜色，增强信息传达\n", "- 参考线系统：添加多条参考线帮助判断表现水平\n", "- 数据排序：自动排序并显示排名信息\n", "- 综合分析：在多个子图基础上提供汇总视图"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 💪 动手练习\n", "\n", "### 练习1：基础柱状图练习\n", "**题目**：使用提供的数据创建一个基本的柱状图，要求包含标题、坐标轴标签和数值标签\n", "\n", "**数据准备**："]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 练习数据 - 不同品牌手机销量\n", "brands = ['华为', '苹果', '小米', 'OPPO', 'vivo']\n", "sales = [2800, 2200, 1800, 1500, 1200]  # 单位：万台\n", "\n", "print(\"手机销量数据已准备完成：\")\n", "for brand, sale in zip(brands, sales):\n", "    print(f\"{brand}: {sale}万台\")\n", "print(\"\\n请在下面创建柱状图：\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# TODO: 在这里创建你的柱状图\n", "# 提示：使用 plt.bar(brands, sales)\n", "\n", "# TODO: 添加标题\n", "# 提示：使用 plt.title('手机品牌销量对比')\n", "\n", "# TODO: 添加坐标轴标签\n", "# 提示：使用 plt.xlabel('品牌') 和 plt.ylabel('销量（万台）')\n", "\n", "# TODO: 添加数值标签\n", "# 提示：使用循环和 plt.text() 在每个柱子上方添加数值\n", "\n", "# TODO: 添加网格和显示图表\n", "# 提示：使用 plt.grid(True, alpha=0.3, axis='y') 和 plt.show()\n", "\n", "print(\"请在上面的代码框中完成练习\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["**期望结果**：创建一个显示各品牌手机销量的柱状图，每个柱子上方显示具体数值"]}, {"cell_type": "markdown", "metadata": {}, "source": ["**参考答案**："]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 参考答案（点击运行查看）\n", "plt.figure(figsize=(10, 6))\n", "bars = plt.bar(brands, sales, color=['#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7'], \n", "              alpha=0.8, edgecolor='white', linewidth=2)\n", "\n", "plt.title('手机品牌销量对比', fontsize=14, fontweight='bold')\n", "plt.xlabel('品牌', fontsize=12)\n", "plt.ylabel('销量（万台）', fontsize=12)\n", "\n", "# 添加数值标签\n", "for bar, sale in zip(bars, sales):\n", "    height = bar.get_height()\n", "    plt.text(bar.get_x() + bar.get_width()/2., height + 50,\n", "            f'{sale}万台', ha='center', va='bottom', fontweight='bold')\n", "\n", "plt.grid(True, alpha=0.3, axis='y')\n", "plt.tight_layout()\n", "plt.show()\n", "\n", "print(\"✅ 练习1完成！\")\n", "print(f\"销量冠军：{brands[sales.index(max(sales))]} ({max(sales)}万台)\")\n", "print(f\"总销量：{sum(sales)}万台\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 练习2：进阶练习 - 创建分组柱状图\n", "**题目**：基于给定的数据创建分组柱状图，比较不同季度各产品的销售表现\n", "\n", "**挑战点**：正确设置柱子位置和宽度，添加图例和标签"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 练习2数据\n", "quarters = ['Q1', 'Q2', 'Q3', 'Q4']\n", "product_a_sales = [150, 180, 200, 220]\n", "product_b_sales = [120, 150, 170, 190]\n", "product_c_sales = [100, 130, 160, 180]\n", "\n", "print(\"季度销售数据：\")\n", "print(f\"产品A: {product_a_sales}\")\n", "print(f\"产品B: {product_b_sales}\")\n", "print(f\"产品C: {product_c_sales}\")\n", "print(\"\\n要求：\")\n", "print(\"1. 创建分组柱状图\")\n", "print(\"2. 为每个产品使用不同颜色\")\n", "print(\"3. 添加图例\")\n", "print(\"4. 设置合适的标题和标签\")\n", "\n", "# TODO: 在这里创建分组柱状图\n", "# 提示：\n", "# x = np.arange(len(quarters))\n", "# width = 0.25\n", "# plt.bar(x - width, product_a_sales, width, label='产品A')\n", "# plt.bar(x, product_b_sales, width, label='产品B')\n", "# plt.bar(x + width, product_c_sales, width, label='产品C')\n", "\n", "print(\"请创建分组柱状图\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["**参考答案**："]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 练习2参考答案\n", "x = np.arange(len(quarters))\n", "width = 0.25\n", "\n", "plt.figure(figsize=(12, 6))\n", "\n", "bars1 = plt.bar(x - width, product_a_sales, width, label='产品A', \n", "               color='#FF6B6B', alpha=0.8)\n", "bars2 = plt.bar(x, product_b_sales, width, label='产品B', \n", "               color='#4ECDC4', alpha=0.8)\n", "bars3 = plt.bar(x + width, product_c_sales, width, label='产品C', \n", "               color='#45B7D1', alpha=0.8)\n", "\n", "plt.title('各产品季度销售对比', fontsize=14, fontweight='bold')\n", "plt.xlabel('季度', fontsize=12)\n", "plt.ylabel('销售额（万元）', fontsize=12)\n", "plt.xticks(x, quarters)\n", "plt.legend()\n", "plt.grid(True, alpha=0.3, axis='y')\n", "\n", "# 添加数值标签（可选）\n", "def add_labels(bars, values):\n", "    for bar, value in zip(bars, values):\n", "        height = bar.get_height()\n", "        plt.text(bar.get_x() + bar.get_width()/2., height + 3,\n", "                f'{value}', ha='center', va='bottom', fontsize=9)\n", "\n", "add_labels(bars1, product_a_sales)\n", "add_labels(bars2, product_b_sales)\n", "add_labels(bars3, product_c_sales)\n", "\n", "plt.tight_layout()\n", "plt.show()\n", "\n", "print(\"✅ 练习2完成！\")\n", "print(\"分组柱状图关键点：\")\n", "print(\"- 使用np.arange()创建位置数组\")\n", "print(\"- 通过调整x位置实现分组效果\")\n", "print(\"- width参数控制柱子宽度\")\n", "print(\"- 使用plt.xticks()设置x轴标签\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 练习3：综合应用 - 创建堆叠柱状图\n", "**题目**：基于公司各部门的人员构成数据，创建堆叠柱状图展示男女比例\n", "\n", "**数据背景**：某公司各部门的员工性别分布情况"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 综合练习数据\n", "departments = ['技术部', '销售部', '市场部', '人事部', '财务部']\n", "male_count = [45, 25, 15, 8, 12]\n", "female_count = [15, 35, 25, 22, 18]\n", "\n", "print(\"各部门人员构成数据：\")\n", "for dept, male, female in zip(departments, male_count, female_count):\n", "    total = male + female\n", "    male_pct = male/total*100\n", "    female_pct = female/total*100\n", "    print(f\"{dept}: 男{male}人({male_pct:.1f}%), 女{female}人({female_pct:.1f}%)\")\n", "\n", "print(\"\\n要求：\")\n", "print(\"1. 创建堆叠柱状图\")\n", "print(\"2. 用不同颜色区分男女\")\n", "print(\"3. 添加百分比标签\")\n", "print(\"4. 计算并显示总体男女比例\")\n", "\n", "# 参考答案\n", "plt.figure(figsize=(12, 8))\n", "\n", "# 创建堆叠柱状图\n", "bars1 = plt.bar(departments, male_count, label='男性', \n", "               color='#4A90E2', alpha=0.8)\n", "bars2 = plt.bar(departments, female_count, bottom=male_count, \n", "               label='女性', color='#E24A90', alpha=0.8)\n", "\n", "plt.title('各部门员工性别分布', fontsize=16, fontweight='bold')\n", "plt.xlabel('部门', fontsize=12)\n", "plt.ylabel('人数', fontsize=12)\n", "plt.legend()\n", "plt.grid(True, alpha=0.3, axis='y')\n", "\n", "# 添加百分比标签\n", "for i, (dept, male, female) in enumerate(zip(departments, male_count, female_count)):\n", "    total = male + female\n", "    \n", "    # 男性标签\n", "    plt.text(i, male/2, f'{male/total*100:.1f}%', \n", "            ha='center', va='center', fontweight='bold', color='white')\n", "    \n", "    # 女性标签\n", "    plt.text(i, male + female/2, f'{female/total*100:.1f}%', \n", "            ha='center', va='center', fontweight='bold', color='white')\n", "    \n", "    # 总人数标签\n", "    plt.text(i, total + 1, f'{total}人', \n", "            ha='center', va='bottom', fontweight='bold')\n", "\n", "plt.xticks(rotation=45, ha='right')\n", "plt.tight_layout()\n", "plt.show()\n", "\n", "# 统计分析\n", "total_male = sum(male_count)\n", "total_female = sum(female_count)\n", "total_employees = total_male + total_female\n", "\n", "print(\"\\n📊 统计结果：\")\n", "print(f\"总员工数：{total_employees}人\")\n", "print(f\"男性员工：{total_male}人 ({total_male/total_employees*100:.1f}%)\")\n", "print(f\"女性员工：{total_female}人 ({total_female/total_employees*100:.1f}%)\")\n", "print(f\"男女比例：{total_male}:{total_female}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 🚨 常见问题和解决方案\n", "\n", "### 问题1：柱子标签重叠或显示不全\n", "**错误现象**：x轴标签过长时相互重叠，或者图表边缘被截断\n", "**原因分析**：标签文字过长，图表尺寸不够，或者没有调整布局\n", "**解决方案**："]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 解决标签重叠问题\n", "long_labels = ['非常长的类别名称A', '非常长的类别名称B', '非常长的类别名称C', '非常长的类别名称D']\n", "values = [25, 35, 30, 40]\n", "\n", "fig, axes = plt.subplots(1, 3, figsize=(18, 6))\n", "fig.suptitle('解决标签重叠问题', fontsize=14, fontweight='bold')\n", "\n", "# 问题示例：标签重叠\n", "axes[0].bar(long_labels, values)\n", "axes[0].set_title('问题：标签重叠')\n", "axes[0].grid(True, alpha=0.3, axis='y')\n", "\n", "# 解决方案1：旋转标签\n", "axes[1].bar(long_labels, values, color='orange')\n", "axes[1].set_title('解决方案1：旋转标签')\n", "axes[1].tick_params(axis='x', rotation=45)\n", "axes[1].grid(True, alpha=0.3, axis='y')\n", "\n", "# 解决方案2：使用水平柱状图\n", "axes[2].barh(long_labels, values, color='lightgreen')\n", "axes[2].set_title('解决方案2：水平柱状图')\n", "axes[2].grid(True, alpha=0.3, axis='x')\n", "\n", "plt.tight_layout()\n", "plt.show()\n", "\n", "print(\"💡 解决标签问题的方法：\")\n", "print(\"1. 旋转标签：plt.xticks(rotation=45)\")\n", "print(\"2. 使用水平柱状图：plt.barh()\")\n", "print(\"3. 缩短标签文字或使用缩写\")\n", "print(\"4. 增大图表尺寸：figsize=(width, height)\")\n", "print(\"5. 使用plt.tight_layout()自动调整\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 问题2：分组柱状图位置不正确\n", "**错误现象**：多个系列的柱子重叠或间距不均匀\n", "**原因分析**：x位置计算错误，柱子宽度设置不当\n", "**解决方案**："]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 解决分组柱状图位置问题\n", "categories = ['A', 'B', 'C', 'D']\n", "series1 = [20, 35, 30, 25]\n", "series2 = [25, 30, 35, 30]\n", "series3 = [15, 25, 20, 35]\n", "\n", "fig, axes = plt.subplots(1, 2, figsize=(14, 6))\n", "fig.suptitle('分组柱状图位置调整', fontsize=14, fontweight='bold')\n", "\n", "# 错误示例：位置重叠\n", "x = np.arange(len(categories))\n", "axes[0].bar(x, series1, label='系列1', alpha=0.7)\n", "axes[0].bar(x, series2, label='系列2', alpha=0.7)  # 错误：位置相同\n", "axes[0].bar(x, series3, label='系列3', alpha=0.7)\n", "axes[0].set_title('错误：柱子重叠')\n", "axes[0].set_xticks(x)\n", "axes[0].set_xticklabels(categories)\n", "axes[0].legend()\n", "axes[0].grid(True, alpha=0.3, axis='y')\n", "\n", "# 正确示例：位置分离\n", "width = 0.25\n", "x = np.arange(len(categories))\n", "\n", "axes[1].bar(x - width, series1, width, label='系列1', color='#FF6B6B', alpha=0.8)\n", "axes[1].bar(x, series2, width, label='系列2', color='#4ECDC4', alpha=0.8)\n", "axes[1].bar(x + width, series3, width, label='系列3', color='#45B7D1', alpha=0.8)\n", "\n", "axes[1].set_title('正确：位置分离')\n", "axes[1].set_xticks(x)\n", "axes[1].set_xticklabels(categories)\n", "axes[1].legend()\n", "axes[1].grid(True, alpha=0.3, axis='y')\n", "\n", "plt.tight_layout()\n", "plt.show()\n", "\n", "print(\"🔧 分组柱状图关键点：\")\n", "print(\"1. 使用 np.arange(len(categories)) 创建基础位置\")\n", "print(\"2. 设置合适的 width 值（通常 0.2-0.3）\")\n", "print(\"3. 通过 x±width 调整各系列位置\")\n", "print(\"4. 确保所有系列使用相同的 width 值\")\n", "print(\"5. 使用 set_xticks(x) 设置刻度位置\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 问题3：堆叠柱状图数值计算错误\n", "**错误现象**：堆叠的各部分位置不正确，或者总高度计算错误\n", "**解决方案**："]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 解决堆叠柱状图计算问题\n", "categories = ['Q1', 'Q2', 'Q3', 'Q4']\n", "part1 = [10, 15, 12, 18]\n", "part2 = [8, 12, 15, 10]\n", "part3 = [5, 8, 10, 12]\n", "\n", "plt.figure(figsize=(12, 6))\n", "\n", "# 正确的堆叠方法\n", "bars1 = plt.bar(categories, part1, label='部分1', color='#FF6B6B', alpha=0.8)\n", "bars2 = plt.bar(categories, part2, bottom=part1, label='部分2', color='#4ECDC4', alpha=0.8)\n", "\n", "# 第三部分需要在前两部分的基础上堆叠\n", "bottom_for_part3 = np.array(part1) + np.array(part2)\n", "bars3 = plt.bar(categories, part3, bottom=bottom_for_part3, label='部分3', color='#45B7D1', alpha=0.8)\n", "\n", "plt.title('正确的堆叠柱状图', fontsize=14, fontweight='bold')\n", "plt.xlabel('季度')\n", "plt.ylabel('数值')\n", "plt.legend()\n", "plt.grid(True, alpha=0.3, axis='y')\n", "\n", "# 添加总计标签\n", "totals = np.array(part1) + np.array(part2) + np.array(part3)\n", "for i, total in enumerate(totals):\n", "    plt.text(i, total + 0.5, f'总计: {total}', ha='center', va='bottom', fontweight='bold')\n", "\n", "plt.tight_layout()\n", "plt.show()\n", "\n", "print(\"📊 堆叠柱状图要点：\")\n", "print(\"1. 第一层：直接使用原始数据\")\n", "print(\"2. 第二层：bottom=第一层数据\")\n", "print(\"3. 第三层：bottom=第一层+第二层\")\n", "print(\"4. 使用 np.array() 进行数组运算\")\n", "print(\"5. 验证总计 = 各部分之和\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 📚 知识小结\n", "\n", "### 核心要点\n", "- **基本语法**：`plt.bar(x, height)` 创建垂直柱状图，`plt.barh(y, width)` 创建水平柱状图\n", "- **柱状图类型**：\n", "  - 简单柱状图：单一数据系列的比较\n", "  - 分组柱状图：多个数据系列的并列比较\n", "  - 堆叠柱状图：展示部分与整体的关系\n", "- **位置控制**：分组图通过调整x位置实现，堆叠图通过bottom参数实现\n", "- **美化技巧**：颜色、透明度、边框、标签、网格的合理使用\n", "\n", "### 最佳实践\n", "- **数据排序**：按数值大小排序便于比较和分析\n", "- **颜色选择**：使用有意义的颜色编码，保持一致性\n", "- **标签处理**：长标签考虑旋转或使用水平图\n", "- **数值标注**：在柱子上添加具体数值增强可读性\n", "- **参考线**：添加平均线、目标线等重要参考\n", "\n", "### 适用场景总结\n", "- **简单柱状图**：类别间数值比较、排名展示\n", "- **分组柱状图**：多维度对比分析、时间序列对比\n", "- **堆叠柱状图**：构成分析、占比展示\n", "- **水平柱状图**：长标签显示、排名列表\n", "\n", "### 下节预告\n", "下一节我们将学习**直方图和密度图**的详细用法，包括：\n", "- 直方图的概念和与柱状图的区别\n", "- 分箱策略和参数设置\n", "- 概率密度函数的绘制\n", "- 多个分布的对比展示\n", "\n", "## 🌟 扩展阅读\n", "- [Matplotlib柱状图官方文档](https://matplotlib.org/stable/api/_as_gen/matplotlib.pyplot.bar.html)\n", "- [颜色和样式指南](https://matplotlib.org/stable/tutorials/colors/colors.html)\n", "- [图表设计最佳实践](https://matplotlib.org/stable/tutorials/introductory/customizing.html)\n", "- [数据可视化原则](https://matplotlib.org/stable/tutorials/introductory/usage.html)"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.5"}}, "nbformat": 4, "nbformat_minor": 4}