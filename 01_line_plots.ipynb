{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# 线图（折线图）- 从入门到精通\n", "\n", "## 📖 本节学习目标\n", "- 掌握线图的基本概念和适用场景\n", "- 学会使用matplotlib创建各种类型的线图\n", "- 理解线图的参数设置和美化技巧\n", "- 能够根据数据特点选择合适的线图样式\n", "- 掌握多条线图的对比展示方法"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 导入必要的库\n", "import matplotlib.pyplot as plt\n", "import numpy as np\n", "import pandas as pd\n", "\n", "# 设置中文字体\n", "plt.rcParams['font.sans-serif'] = ['SimHei', 'DejaVu Sans']\n", "plt.rcParams['axes.unicode_minus'] = False\n", "\n", "# 设置图表样式\n", "plt.style.use('default')\n", "\n", "print(\"✅ 环境配置完成！\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 🎯 知识点详解\n", "\n", "### 1. 线图概念介绍\n", "\n", "**定义和特点**：\n", "- 线图是用线段连接数据点的图表类型\n", "- 最适合展示连续数据的变化趋势\n", "- 可以清晰显示数据的上升、下降和波动模式\n", "\n", "**适用数据类型**：\n", "- 时间序列数据（如股价、温度变化）\n", "- 连续变量关系（如函数图像）\n", "- 趋势分析数据（如销售增长）\n", "\n", "**使用场景分析**：\n", "- ✅ 展示数据随时间的变化\n", "- ✅ 比较多个变量的趋势\n", "- ✅ 显示函数关系\n", "- ❌ 展示分类数据的构成比例\n", "- ❌ 显示数据的分布情况\n", "\n", "### 2. 基础语法和参数\n", "\n", "**核心函数**：`plt.plot(x, y, format_string, **kwargs)`\n", "\n", "**必需参数**：\n", "- `x`: x轴数据（可选，如果省略则使用索引）\n", "- `y`: y轴数据（必需）\n", "\n", "**重要可选参数**：\n", "- `color/c`: 线条颜色\n", "- `linewidth/lw`: 线条宽度\n", "- `linestyle/ls`: 线条样式\n", "- `marker`: 数据点标记\n", "- `markersize/ms`: 标记大小\n", "- `label`: 图例标签\n", "- `alpha`: 透明度"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 3. 代码演示（从简单到复杂）\n", "\n", "#### 3.1 最简单示例"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 最基础的线图\n", "y = [1, 4, 2, 3, 5]  # 只提供y值，x值自动为索引\n", "\n", "plt.figure(figsize=(8, 6))  # 设置图表大小\n", "plt.plot(y)  # 绘制线图\n", "plt.title('最简单的线图示例')  # 添加标题\n", "plt.xlabel('索引')  # x轴标签\n", "plt.ylabel('数值')  # y轴标签\n", "plt.grid(True, alpha=0.3)  # 添加网格\n", "plt.show()\n", "\n", "print(\"📊 这是最基础的线图，x轴自动使用数据索引\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 指定x和y数据的线图\n", "x = [1, 2, 3, 4, 5]  # x轴数据\n", "y = [2, 8, 3, 6, 10]  # y轴数据\n", "\n", "plt.figure(figsize=(8, 6))\n", "plt.plot(x, y)  # 同时指定x和y\n", "plt.title('指定X和Y数据的线图')\n", "plt.xlabel('X轴数据')\n", "plt.ylabel('Y轴数据')\n", "plt.grid(True, alpha=0.3)\n", "plt.show()\n", "\n", "print(\"📊 指定x和y数据可以更精确地控制图表\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### 3.2 参数定制示例"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 线条样式定制\n", "x = np.linspace(0, 10, 20)  # 生成0到10之间的20个点\n", "y = np.sin(x)  # 计算正弦值\n", "\n", "plt.figure(figsize=(12, 8))\n", "\n", "# 创建2x2的子图布局\n", "plt.subplot(2, 2, 1)\n", "plt.plot(x, y, color='red', linewidth=2)  # 红色粗线\n", "plt.title('红色粗线')\n", "plt.grid(True, alpha=0.3)\n", "\n", "plt.subplot(2, 2, 2)\n", "plt.plot(x, y, color='blue', linestyle='--', linewidth=1.5)  # 蓝色虚线\n", "plt.title('蓝色虚线')\n", "plt.grid(True, alpha=0.3)\n", "\n", "plt.subplot(2, 2, 3)\n", "plt.plot(x, y, color='green', marker='o', markersize=6)  # 绿色带圆点标记\n", "plt.title('绿色带标记点')\n", "plt.grid(True, alpha=0.3)\n", "\n", "plt.subplot(2, 2, 4)\n", "plt.plot(x, y, 'ro-', linewidth=2, markersize=8, alpha=0.7)  # 组合样式\n", "plt.title('组合样式（红色圆点实线）')\n", "plt.grid(True, alpha=0.3)\n", "\n", "plt.tight_layout()  # 自动调整子图间距\n", "plt.show()\n", "\n", "print(\"🎨 不同的线条样式可以传达不同的信息\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 线条样式参数详解\n", "x = np.linspace(0, 8, 50)\n", "\n", "plt.figure(figsize=(14, 10))\n", "\n", "# 不同线型展示\n", "linestyles = ['-', '--', '-.', ':']\n", "linestyle_names = ['实线 (-)', '虚线 (--)', '点划线 (-.)', '点线 (:)']\n", "\n", "for i, (ls, name) in enumerate(zip(linestyles, linestyle_names)):\n", "    y = np.sin(x + i * 0.5) + i * 0.5  # 稍微偏移以便区分\n", "    plt.plot(x, y, linestyle=ls, linewidth=2.5, label=name)\n", "\n", "plt.title('不同线型样式对比', fontsize=16, fontweight='bold')\n", "plt.xlabel('X轴', fontsize=12)\n", "plt.ylabel('Y轴', fontsize=12)\n", "plt.legend(fontsize=11)  # 显示图例\n", "plt.grid(True, alpha=0.3)\n", "plt.show()\n", "\n", "print(\"📏 线型选择指南：\")\n", "print(\"- 实线(-)：主要数据或重要趋势\")\n", "print(\"- 虚线(--)：次要数据或预测数据\")\n", "print(\"- 点划线(-.)：参考线或基准线\")\n", "print(\"- 点线(:)：辅助信息或背景数据\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### 3.3 美化和进阶示例"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 多条线图对比 - 专业美化版本\n", "x = np.linspace(0, 12, 100)\n", "y1 = np.sin(x)\n", "y2 = np.cos(x)\n", "y3 = np.sin(x) * np.cos(x)\n", "\n", "# 创建图表\n", "fig, ax = plt.subplots(figsize=(12, 8))\n", "\n", "# 绘制多条线，使用不同颜色和样式\n", "line1 = ax.plot(x, y1, color='#FF6B6B', linewidth=2.5, label='sin(x)', alpha=0.8)\n", "line2 = ax.plot(x, y2, color='#4ECDC4', linewidth=2.5, label='cos(x)', alpha=0.8)\n", "line3 = ax.plot(x, y3, color='#45B7D1', linewidth=2.5, label='sin(x)×cos(x)', alpha=0.8)\n", "\n", "# 美化标题和标签\n", "ax.set_title('三角函数对比图', fontsize=18, fontweight='bold', pad=20)\n", "ax.set_xlabel('角度 (弧度)', fontsize=14)\n", "ax.set_ylabel('函数值', fontsize=14)\n", "\n", "# 设置坐标轴\n", "ax.set_xlim(0, 12)\n", "ax.set_ylim(-1.2, 1.2)\n", "\n", "# 添加网格\n", "ax.grid(True, alpha=0.3, linestyle='--')\n", "\n", "# 美化图例\n", "legend = ax.legend(loc='upper right', fontsize=12, frameon=True, \n", "                  fancybox=True, shadow=True, framealpha=0.9)\n", "legend.get_frame().set_facecolor('white')\n", "\n", "# 设置背景色\n", "ax.set_facecolor('#F8F9FA')\n", "fig.patch.set_facecolor('white')\n", "\n", "# 添加水平参考线\n", "ax.axhline(y=0, color='black', linestyle='-', alpha=0.3, linewidth=0.8)\n", "\n", "# 调整布局\n", "plt.tight_layout()\n", "plt.show()\n", "\n", "print(\"✨ 专业级多线图完成！注意颜色搭配和视觉层次\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 🔍 举一反三\n", "\n", "### 场景1：股票价格趋势分析\n", "**问题描述**：需要展示某只股票一个月内的价格变化趋势，包括开盘价、收盘价和移动平均线\n", "**解决思路**：使用多条线图展示不同价格指标，通过颜色和线型区分\n", "**代码实现**："]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 模拟股票数据\n", "np.random.seed(42)  # 设置随机种子，确保结果可重现\n", "days = pd.date_range('2024-01-01', periods=30, freq='D')\n", "\n", "# 生成模拟股价数据\n", "base_price = 100\n", "price_changes = np.random.normal(0, 2, 30).cumsum()  # 累积随机变化\n", "stock_prices = base_price + price_changes\n", "\n", "# 计算5日移动平均线\n", "ma5 = pd.Series(stock_prices).rolling(window=5).mean()\n", "\n", "# 创建专业的股价图表\n", "fig, ax = plt.subplots(figsize=(14, 8))\n", "\n", "# 绘制股价线\n", "ax.plot(days, stock_prices, color='#2E86AB', linewidth=2, \n", "        label='股价', marker='o', markersize=4, alpha=0.8)\n", "\n", "# 绘制移动平均线\n", "ax.plot(days, ma5, color='#F18F01', linewidth=2.5, \n", "        label='5日移动平均', linestyle='--', alpha=0.9)\n", "\n", "# 美化图表\n", "ax.set_title('股票价格趋势分析', fontsize=16, fontweight='bold', pad=20)\n", "ax.set_xlabel('日期', fontsize=12)\n", "ax.set_ylabel('价格 (元)', fontsize=12)\n", "\n", "# 格式化x轴日期显示\n", "ax.tick_params(axis='x', rotation=45)\n", "\n", "# 添加网格和图例\n", "ax.grid(True, alpha=0.3, linestyle=':')\n", "ax.legend(loc='upper left', fontsize=11)\n", "\n", "# 高亮显示最高点和最低点\n", "max_idx = np.argmax(stock_prices)\n", "min_idx = np.argmin(stock_prices)\n", "\n", "ax.annotate(f'最高点\\n{stock_prices[max_idx]:.2f}元', \n", "           xy=(days[max_idx], stock_prices[max_idx]),\n", "           xytext=(10, 20), textcoords='offset points',\n", "           bbox=dict(boxstyle='round,pad=0.3', facecolor='red', alpha=0.7),\n", "           arrowprops=dict(arrowstyle='->', connectionstyle='arc3,rad=0'))\n", "\n", "ax.annotate(f'最低点\\n{stock_prices[min_idx]:.2f}元', \n", "           xy=(days[min_idx], stock_prices[min_idx]),\n", "           xytext=(10, -30), textcoords='offset points',\n", "           bbox=dict(boxstyle='round,pad=0.3', facecolor='green', alpha=0.7),\n", "           arrowprops=dict(arrowstyle='->', connectionstyle='arc3,rad=0'))\n", "\n", "plt.tight_layout()\n", "plt.show()\n", "\n", "print(f\"📈 股价分析完成！\")\n", "print(f\"📊 最高价: {stock_prices.max():.2f}元\")\n", "print(f\"📉 最低价: {stock_prices.min():.2f}元\")\n", "print(f\"📈 涨跌幅: {((stock_prices[-1] - stock_prices[0]) / stock_prices[0] * 100):.2f}%\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["**知识扩展**：\n", "- 使用`pd.date_range()`创建时间序列\n", "- `rolling().mean()`计算移动平均线\n", "- `annotate()`添加注释和箭头\n", "- 通过颜色编码传达信息（红色=高点，绿色=低点）"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 场景2：科学实验数据展示\n", "**问题描述**：展示温度传感器在24小时内的读数变化，包括误差范围\n", "**解决思路**：使用线图展示主要趋势，添加误差带显示数据不确定性\n", "**代码实现**："]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 模拟24小时温度数据\n", "hours = np.arange(0, 24, 0.5)  # 每30分钟一个数据点\n", "base_temp = 20 + 5 * np.sin(2 * np.pi * hours / 24)  # 基础温度变化（正弦波模拟日夜变化）\n", "noise = np.random.normal(0, 0.5, len(hours))  # 添加噪声\n", "temperature = base_temp + noise\n", "\n", "# 计算误差范围（假设传感器精度为±1°C）\n", "error_range = 1.0\n", "temp_upper = temperature + error_range\n", "temp_lower = temperature - error_range\n", "\n", "# 创建图表\n", "fig, ax = plt.subplots(figsize=(14, 8))\n", "\n", "# 绘制误差带（填充区域）\n", "ax.fill_between(hours, temp_lower, temp_upper, \n", "                alpha=0.3, color='lightblue', label='误差范围 (±1°C)')\n", "\n", "# 绘制主要温度线\n", "ax.plot(hours, temperature, color='#E74C3C', linewidth=2.5, \n", "        label='实测温度', marker='o', markersize=3, alpha=0.8)\n", "\n", "# 绘制理论温度线（无噪声）\n", "ax.plot(hours, base_temp, color='#3498DB', linewidth=2, \n", "        label='理论温度', linestyle='--', alpha=0.7)\n", "\n", "# 美化图表\n", "ax.set_title('24小时温度监测数据', fontsize=16, fontweight='bold')\n", "ax.set_xlabel('时间 (小时)', fontsize=12)\n", "ax.set_ylabel('温度 (°C)', fontsize=12)\n", "\n", "# 设置x轴刻度\n", "ax.set_xticks(range(0, 25, 4))\n", "ax.set_xticklabels([f'{h:02d}:00' for h in range(0, 25, 4)])\n", "\n", "# 添加网格和图例\n", "ax.grid(True, alpha=0.3, linestyle=':')\n", "ax.legend(loc='upper right', fontsize=11)\n", "\n", "# 添加统计信息文本框\n", "stats_text = f\"\"\"统计信息：\n", "平均温度: {temperature.mean():.1f}°C\n", "最高温度: {temperature.max():.1f}°C\n", "最低温度: {temperature.min():.1f}°C\n", "标准差: {temperature.std():.2f}°C\"\"\"\n", "\n", "ax.text(0.02, 0.98, stats_text, transform=ax.transAxes, \n", "        verticalalignment='top', bbox=dict(boxstyle='round', \n", "        facecolor='wheat', alpha=0.8), fontsize=10)\n", "\n", "plt.tight_layout()\n", "plt.show()\n", "\n", "print(\"🌡️ 温度监测数据分析完成！\")\n", "print(\"💡 误差带显示了测量的不确定性范围\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["**知识扩展**：\n", "- `fill_between()`创建误差带\n", "- `text()`添加统计信息文本框\n", "- `transform=ax.transAxes`使用相对坐标定位\n", "- 科学数据可视化的最佳实践"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 场景3：多维数据综合展示\n", "**问题描述**：同时展示网站的访问量、转化率和收入三个指标的月度变化\n", "**解决思路**：使用双y轴技术，左轴显示访问量，右轴显示转化率和收入\n", "**代码实现**："]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 模拟网站数据\n", "months = ['1月', '2月', '3月', '4月', '5月', '6月', \n", "          '7月', '8月', '9月', '10月', '11月', '12月']\n", "\n", "# 访问量数据（单位：万）\n", "visits = [120, 135, 148, 162, 178, 195, 210, 225, 240, 255, 270, 285]\n", "\n", "# 转化率数据（单位：%）\n", "conversion_rate = [2.1, 2.3, 2.5, 2.4, 2.7, 2.9, 3.1, 3.0, 3.2, 3.4, 3.6, 3.8]\n", "\n", "# 收入数据（单位：万元）\n", "revenue = [25, 31, 37, 39, 48, 57, 65, 68, 77, 87, 97, 108]\n", "\n", "# 创建图表和双y轴\n", "fig, ax1 = plt.subplots(figsize=(14, 8))\n", "ax2 = ax1.twinx()  # 创建共享x轴的第二个y轴\n", "\n", "# 在左y轴绘制访问量\n", "line1 = ax1.plot(months, visits, color='#3498DB', linewidth=3, \n", "                 marker='o', markersize=8, label='访问量')\n", "ax1.set_ylabel('访问量 (万)', fontsize=12, color='#3498DB')\n", "ax1.tick_params(axis='y', labelcolor='#3498DB')\n", "\n", "# 在右y轴绘制转化率和收入\n", "line2 = ax2.plot(months, conversion_rate, color='#E74C3C', linewidth=2.5, \n", "                 marker='s', markersize=6, label='转化率', linestyle='--')\n", "line3 = ax2.plot(months, revenue, color='#2ECC71', linewidth=2.5, \n", "                 marker='^', markersize=6, label='收入')\n", "ax2.set_ylabel('转化率 (%) / 收入 (万元)', fontsize=12, color='#E74C3C')\n", "ax2.tick_params(axis='y', labelcolor='#E74C3C')\n", "\n", "# 设置标题和x轴\n", "ax1.set_title('网站关键指标月度趋势分析', fontsize=16, fontweight='bold', pad=20)\n", "ax1.set_xlabel('月份', fontsize=12)\n", "\n", "# 旋转x轴标签\n", "plt.setp(ax1.get_xticklabels(), rotation=45, ha='right')\n", "\n", "# 添加网格\n", "ax1.grid(True, alpha=0.3, linestyle=':')\n", "\n", "# 合并图例\n", "lines1, labels1 = ax1.get_legend_handles_labels()\n", "lines2, labels2 = ax2.get_legend_handles_labels()\n", "ax1.legend(lines1 + lines2, labels1 + labels2, loc='upper left', fontsize=11)\n", "\n", "# 添加数据标签（仅在关键点）\n", "for i in [0, 5, 11]:  # 1月、6月、12月\n", "    ax1.annotate(f'{visits[i]}万', (i, visits[i]), \n", "                textcoords=\"offset points\", xytext=(0,10), ha='center')\n", "    ax2.annotate(f'{revenue[i]}万', (i, revenue[i]), \n", "                textcoords=\"offset points\", xytext=(0,10), ha='center')\n", "\n", "plt.tight_layout()\n", "plt.show()\n", "\n", "# 计算并显示关键指标\n", "visit_growth = ((visits[-1] - visits[0]) / visits[0]) * 100\n", "revenue_growth = ((revenue[-1] - revenue[0]) / revenue[0]) * 100\n", "\n", "print(f\"📊 年度数据分析：\")\n", "print(f\"📈 访问量增长: {visit_growth:.1f}%\")\n", "print(f\"💰 收入增长: {revenue_growth:.1f}%\")\n", "print(f\"🎯 平均转化率: {np.mean(conversion_rate):.2f}%\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["**知识扩展**：\n", "- `twinx()`创建双y轴图表\n", "- 不同数量级数据的可视化技巧\n", "- 图例合并的方法\n", "- 数据标签的选择性添加\n", "- 商业数据分析的可视化最佳实践"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 💪 动手练习\n", "\n", "### 练习1：基础练习\n", "**题目**：使用提供的数据创建一个基本的线图，要求包含标题、坐标轴标签、网格和图例\n", "\n", "**数据准备**："]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 练习数据 - 某城市一周的气温变化\n", "days = ['周一', '周二', '周三', '周四', '周五', '周六', '周日']\n", "high_temp = [28, 30, 32, 29, 27, 25, 26]  # 最高温度\n", "low_temp = [18, 20, 22, 19, 17, 15, 16]   # 最低温度\n", "\n", "# TODO: 在这里创建你的图表\n", "# 要求：\n", "# 1. 创建一个图表，同时显示最高温度和最低温度\n", "# 2. 使用不同的颜色和线型区分两条线\n", "# 3. 添加适当的标题和轴标签\n", "# 4. 显示图例\n", "# 5. 添加网格\n", "\n", "# 提示：使用 plt.plot() 两次，分别绘制高温和低温\n", "# 提示：使用 label 参数为每条线添加标签\n", "# 提示：使用 plt.legend() 显示图例\n", "\n", "print(\"请在上面完成练习1\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["**参考答案**："]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 参考答案\n", "plt.figure(figsize=(10, 6))\n", "\n", "# 绘制最高温度线\n", "plt.plot(days, high_temp, color='red', linewidth=2.5, \n", "         marker='o', markersize=6, label='最高温度')\n", "\n", "# 绘制最低温度线\n", "plt.plot(days, low_temp, color='blue', linewidth=2.5, \n", "         marker='s', markersize=6, label='最低温度')\n", "\n", "# 添加标题和标签\n", "plt.title('一周气温变化趋势', fontsize=16, fontweight='bold')\n", "plt.xlabel('日期', fontsize=12)\n", "plt.ylabel('温度 (°C)', fontsize=12)\n", "\n", "# 添加图例和网格\n", "plt.legend(fontsize=11)\n", "plt.grid(True, alpha=0.3)\n", "\n", "# 旋转x轴标签以避免重叠\n", "plt.xticks(rotation=45)\n", "\n", "plt.tight_layout()\n", "plt.show()\n", "\n", "print(\"✅ 练习1完成！\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 练习2：进阶练习\n", "**题目**：修正下面代码中的错误，并按要求完善功能\n", "\n", "**有问题的代码**："]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 有问题的代码 - 请找出并修正错误\n", "import matplotlib.pyplot as plt\n", "import numpy as np\n", "\n", "# 数据\n", "x = np.linspace(0, 10, 50)\n", "y1 = np.sin(x)\n", "y2 = np.cos(x)\n", "\n", "# 绘图（有多个问题）\n", "plt.plot(x, y1, label='sin(x)')  # 问题1：没有设置图表大小\n", "plt.plot(x, y2, label='cos(x)')  # 问题2：颜色可能重复\n", "plt.title('三角函数')  # 问题3：可能的中文显示问题\n", "plt.legend()  # 问题4：图例位置可能不合适\n", "# 问题5：缺少坐标轴标签\n", "# 问题6：没有网格\n", "# 问题7：没有调用plt.show()\n", "\n", "print(\"请修正上面代码中的所有问题\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["**要求**：\n", "1. 修正所有错误\n", "2. 设置合适的图表大小\n", "3. 使用不同颜色和线型\n", "4. 添加坐标轴标签和网格\n", "5. 优化图例位置\n", "6. 确保中文正常显示\n", "\n", "**修正后的代码**："]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 修正后的代码\n", "import matplotlib.pyplot as plt\n", "import numpy as np\n", "\n", "# 确保中文显示正常\n", "plt.rcParams['font.sans-serif'] = ['SimHei', 'DejaVu Sans']\n", "plt.rcParams['axes.unicode_minus'] = False\n", "\n", "# 数据\n", "x = np.linspace(0, 10, 50)\n", "y1 = np.sin(x)\n", "y2 = np.cos(x)\n", "\n", "# 创建图表并设置大小\n", "plt.figure(figsize=(10, 6))\n", "\n", "# 绘图，使用不同颜色和线型\n", "plt.plot(x, y1, color='red', linewidth=2, linestyle='-', \n", "         marker='o', markersize=4, label='sin(x)')\n", "plt.plot(x, y2, color='blue', linewidth=2, linestyle='--', \n", "         marker='s', markersize=4, label='cos(x)')\n", "\n", "# 添加标题和坐标轴标签\n", "plt.title('三角函数对比图', fontsize=16, fontweight='bold')\n", "plt.xlabel('x值', fontsize=12)\n", "plt.ylabel('函数值', fontsize=12)\n", "\n", "# 添加网格和图例\n", "plt.grid(True, alpha=0.3, linestyle=':')\n", "plt.legend(loc='upper right', fontsize=11)\n", "\n", "# 调整布局并显示\n", "plt.tight_layout()\n", "plt.show()\n", "\n", "print(\"✅ 所有问题已修正！\")\n", "print(\"🔧 修正内容：\")\n", "print(\"- 添加了图表大小设置\")\n", "print(\"- 使用了不同的颜色和线型\")\n", "print(\"- 添加了坐标轴标签\")\n", "print(\"- 添加了网格\")\n", "print(\"- 优化了图例位置\")\n", "print(\"- 确保了中文显示\")\n", "print(\"- 添加了plt.show()\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 练习3：综合应用\n", "**题目**：创建一个展示公司季度业绩的综合图表\n", "\n", "**数据背景**：某公司2023年四个季度的销售额、利润和员工数量数据\n", "\n", "**数据准备**："]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 综合练习数据\n", "quarters = ['Q1', 'Q2', 'Q3', 'Q4']\n", "sales = [1200, 1450, 1680, 1920]      # 销售额（万元）\n", "profit = [180, 220, 280, 350]         # 利润（万元）\n", "employees = [45, 48, 52, 55]          # 员工数量（人）\n", "\n", "# 自由发挥要求：\n", "# 1. 创建一个包含所有三个指标的图表\n", "# 2. 合理使用双y轴技术（因为数据量级不同）\n", "# 3. 添加数据标签显示具体数值\n", "# 4. 使用专业的配色方案\n", "# 5. 添加趋势分析的文字说明\n", "# 6. 计算并显示增长率\n", "\n", "# 在这里实现你的解决方案\n", "# 提示：可以参考前面场景3的双y轴示例\n", "\n", "print(\"请在上面完成综合练习\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["**参考解决方案**："]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 综合练习参考解决方案\n", "fig, ax1 = plt.subplots(figsize=(12, 8))\n", "ax2 = ax1.twinx()\n", "\n", "# 左y轴：销售额和利润\n", "line1 = ax1.plot(quarters, sales, color='#2E86AB', linewidth=3, \n", "                 marker='o', markersize=8, label='销售额')\n", "line2 = ax1.plot(quarters, profit, color='#A23B72', linewidth=3, \n", "                 marker='s', markersize=8, label='利润')\n", "\n", "ax1.set_ylabel('金额 (万元)', fontsize=12, color='#2E86AB')\n", "ax1.tick_params(axis='y', labelcolor='#2E86AB')\n", "\n", "# 右y轴：员工数量\n", "line3 = ax2.plot(quarters, employees, color='#F18F01', linewidth=3, \n", "                 marker='^', markersize=8, label='员工数量')\n", "\n", "ax2.set_ylabel('员工数量 (人)', fontsize=12, color='#F18F01')\n", "ax2.tick_params(axis='y', labelcolor='#F18F01')\n", "\n", "# 标题和x轴\n", "ax1.set_title('2023年公司季度业绩综合分析', fontsize=16, fontweight='bold', pad=20)\n", "ax1.set_xlabel('季度', fontsize=12)\n", "\n", "# 添加数据标签\n", "for i, (q, s, p, e) in enumerate(zip(quarters, sales, profit, employees)):\n", "    ax1.annotate(f'{s}万', (i, s), textcoords=\"offset points\", \n", "                xytext=(0,10), ha='center', fontsize=9)\n", "    ax1.annotate(f'{p}万', (i, p), textcoords=\"offset points\", \n", "                xytext=(0,-15), ha='center', fontsize=9)\n", "    ax2.annotate(f'{e}人', (i, e), textcoords=\"offset points\", \n", "                xytext=(0,10), ha='center', fontsize=9)\n", "\n", "# 合并图例\n", "lines1, labels1 = ax1.get_legend_handles_labels()\n", "lines2, labels2 = ax2.get_legend_handles_labels()\n", "ax1.legend(lines1 + lines2, labels1 + labels2, loc='upper left')\n", "\n", "# 添加网格\n", "ax1.grid(True, alpha=0.3, linestyle=':')\n", "\n", "# 添加趋势分析文本\n", "analysis_text = \"\"\"业绩分析：\n", "• 销售额稳步增长60%\n", "• 利润增长率达94%\n", "• 团队规模扩大22%\n", "• 盈利能力显著提升\"\"\"\n", "\n", "ax1.text(0.98, 0.02, analysis_text, transform=ax1.transAxes, \n", "         verticalalignment='bottom', horizontalalignment='right',\n", "         bbox=dict(boxstyle='round', facecolor='lightgray', alpha=0.8),\n", "         fontsize=10)\n", "\n", "plt.tight_layout()\n", "plt.show()\n", "\n", "# 计算增长率\n", "sales_growth = ((sales[-1] - sales[0]) / sales[0]) * 100\n", "profit_growth = ((profit[-1] - profit[0]) / profit[0]) * 100\n", "employee_growth = ((employees[-1] - employees[0]) / employees[0]) * 100\n", "\n", "print(f\"📊 2023年业绩总结：\")\n", "print(f\"📈 销售额增长: {sales_growth:.1f}%\")\n", "print(f\"💰 利润增长: {profit_growth:.1f}%\")\n", "print(f\"👥 员工增长: {employee_growth:.1f}%\")\n", "print(f\"🎯 利润率提升: {(profit[-1]/sales[-1] - profit[0]/sales[0])*100:.1f}个百分点\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["**思考题**：\n", "1. **为什么选择线图？** 因为要展示时间序列数据的趋势变化\n", "2. **如果数据量增加10倍怎么办？** 可以考虑数据采样、滚动平均或交互式图表\n", "3. **如何让图表更有说服力？** 添加基准线、目标线、预测线和详细的数据分析\n", "\n", "**评价标准**：\n", "- ✅ 代码正确性：语法正确，能正常运行\n", "- ✅ 图表美观度：配色协调，布局合理\n", "- ✅ 创新性：有独特的设计思路\n", "- ✅ 实用性：符合实际业务需求"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 🚨 常见问题和解决方案\n", "\n", "### 问题1：线条重叠难以区分\n", "**错误现象**：多条线图中线条颜色相似或重叠，难以区分\n", "**原因分析**：默认颜色循环或线型设置不当\n", "**解决方案**："]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 解决线条重叠问题\n", "x = np.linspace(0, 10, 50)\n", "y1 = np.sin(x)\n", "y2 = np.sin(x + 0.1)  # 故意创建相似的线条\n", "y3 = np.sin(x + 0.2)\n", "\n", "# 问题示例（线条难以区分）\n", "plt.figure(figsize=(12, 5))\n", "\n", "plt.subplot(1, 2, 1)\n", "plt.plot(x, y1, label='线条1')\n", "plt.plot(x, y2, label='线条2')\n", "plt.plot(x, y3, label='线条3')\n", "plt.title('❌ 问题：线条难以区分')\n", "plt.legend()\n", "plt.grid(True, alpha=0.3)\n", "\n", "# 解决方案（清晰区分）\n", "plt.subplot(1, 2, 2)\n", "colors = ['#E74C3C', '#3498DB', '#2ECC71']\n", "linestyles = ['-', '--', '-.']\n", "markers = ['o', 's', '^']\n", "\n", "for i, (y, color, ls, marker) in enumerate(zip([y1, y2, y3], colors, linestyles, markers)):\n", "    plt.plot(x, y, color=color, linestyle=ls, linewidth=2.5,\n", "             marker=marker, markersize=4, markevery=5,  # 每5个点显示一个标记\n", "             label=f'线条{i+1}')\n", "\n", "plt.title('✅ 解决：清晰区分线条')\n", "plt.legend()\n", "plt.grid(True, alpha=0.3)\n", "\n", "plt.tight_layout()\n", "plt.show()\n", "\n", "print(\"💡 解决方案：\")\n", "print(\"- 使用对比鲜明的颜色\")\n", "print(\"- 采用不同的线型\")\n", "print(\"- 添加不同的标记符号\")\n", "print(\"- 使用markevery参数控制标记密度\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 问题2：数据点过多导致图表混乱\n", "**错误现象**：数据点太多，线条看起来很粗糙或混乱\n", "**解决方案**："]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 处理大量数据点的问题\n", "# 生成大量数据点\n", "x_dense = np.linspace(0, 10, 1000)  # 1000个点\n", "y_dense = np.sin(x_dense) + 0.1 * np.random.randn(1000)  # 添加噪声\n", "\n", "plt.figure(figsize=(15, 5))\n", "\n", "# 问题示例：直接绘制所有点\n", "plt.subplot(1, 3, 1)\n", "plt.plot(x_dense, y_dense, 'o-', markersize=2, linewidth=0.5)\n", "plt.title('❌ 问题：数据点过多')\n", "plt.grid(True, alpha=0.3)\n", "\n", "# 解决方案1：数据采样\n", "plt.subplot(1, 3, 2)\n", "step = 20  # 每20个点取一个\n", "plt.plot(x_dense[::step], y_dense[::step], 'o-', \n", "         markersize=4, linewidth=2, color='red')\n", "plt.title('✅ 解决方案1：数据采样')\n", "plt.grid(True, alpha=0.3)\n", "\n", "# 解决方案2：移动平均平滑\n", "plt.subplot(1, 3, 3)\n", "window_size = 50\n", "y_smooth = pd.Series(y_dense).rolling(window=window_size, center=True).mean()\n", "plt.plot(x_dense, y_smooth, linewidth=3, color='green', alpha=0.8)\n", "plt.plot(x_dense, y_dense, alpha=0.3, linewidth=0.5, color='gray', label='原始数据')\n", "plt.title('✅ 解决方案2：移动平均平滑')\n", "plt.legend()\n", "plt.grid(True, alpha=0.3)\n", "\n", "plt.tight_layout()\n", "plt.show()\n", "\n", "print(\"🔧 大数据量处理技巧：\")\n", "print(\"- 数据采样：减少显示的数据点数量\")\n", "print(\"- 移动平均：平滑噪声，突出趋势\")\n", "print(\"- 透明度：原始数据用低透明度显示\")\n", "print(\"- 交互式图表：使用plotly等库支持缩放\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 问题3：坐标轴范围设置不当\n", "**错误现象**：重要数据被压缩在图表的一小部分区域\n", "**解决方案**："]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 坐标轴范围优化\n", "x = np.linspace(0, 10, 50)\n", "y = 1000 + 5 * np.sin(x)  # 数据在1000附近小幅波动\n", "\n", "plt.figure(figsize=(12, 5))\n", "\n", "# 问题示例：使用默认范围\n", "plt.subplot(1, 2, 1)\n", "plt.plot(x, y, 'o-', linewidth=2, markersize=4)\n", "plt.title('❌ 问题：变化趋势不明显')\n", "plt.ylabel('数值')\n", "plt.grid(True, alpha=0.3)\n", "\n", "# 解决方案：调整y轴范围突出变化\n", "plt.subplot(1, 2, 2)\n", "plt.plot(x, y, 'o-', linewidth=2, markersize=4, color='red')\n", "plt.ylim(995, 1005)  # 设置合适的y轴范围\n", "plt.title('✅ 解决：突出数据变化')\n", "plt.ylabel('数值')\n", "plt.grid(True, alpha=0.3)\n", "\n", "# 添加断轴标记（可选）\n", "plt.axhline(y=995, color='black', linestyle='--', alpha=0.5)\n", "plt.text(0.5, 995.5, '... (省略0-995)', fontsize=8, alpha=0.7)\n", "\n", "plt.tight_layout()\n", "plt.show()\n", "\n", "print(\"📏 坐标轴设置技巧：\")\n", "print(\"- 使用plt.xlim()和plt.ylim()设置范围\")\n", "print(\"- 关注数据的实际变化范围\")\n", "print(\"- 考虑添加断轴标记说明省略的部分\")\n", "print(\"- 保持坐标轴比例的合理性\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 📚 知识小结\n", "\n", "### 核心要点\n", "- **基本语法**：`plt.plot(x, y, format_string, **kwargs)` 是线图的核心函数\n", "- **样式控制**：通过color、linewidth、linestyle、marker等参数控制外观\n", "- **多线对比**：使用不同颜色、线型和标记区分多条线\n", "- **双轴技术**：`twinx()`处理不同量级的数据\n", "- **数据处理**：大数据量时考虑采样或平滑处理\n", "- **美化技巧**：合理设置坐标轴范围、添加注释和图例\n", "\n", "### 适用场景总结\n", "- ✅ **时间序列数据**：股价、温度、销售额等随时间变化的数据\n", "- ✅ **函数关系**：数学函数、科学实验数据\n", "- ✅ **趋势分析**：业务指标的变化趋势\n", "- ✅ **多变量对比**：多个指标的同期比较\n", "\n", "### 下节预告\n", "下一节我们将学习**散点图**的详细用法，包括：\n", "- 基本散点图的创建\n", "- 气泡图和分类散点图\n", "- 相关性分析可视化\n", "- 散点图的美化和注释技巧\n", "\n", "## 🌟 扩展阅读\n", "- [Matplotlib线图官方文档](https://matplotlib.org/stable/api/_as_gen/matplotlib.pyplot.plot.html)\n", "- [线条样式参考](https://matplotlib.org/stable/gallery/lines_bars_and_markers/linestyles.html)\n", "- [颜色映射指南](https://matplotlib.org/stable/tutorials/colors/colormaps.html)\n", "- [时间序列可视化最佳实践](https://matplotlib.org/stable/gallery/text_labels_and_annotations/date.html)"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.5"}}, "nbformat": 4, "nbformat_minor": 4}