{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Matplotlib绘图基础 - 开启数据可视化之旅\n", "\n", "## 📖 本节学习目标\n", "- 了解matplotlib的基本概念和重要性\n", "- 掌握matplotlib的安装和基本导入方法\n", "- 理解matplotlib的核心组件和架构\n", "- 学会创建第一个matplotlib图表\n", "- 解决中文显示问题\n", "- 为后续深入学习打下坚实基础"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 🎯 什么是Matplotlib？\n", "\n", "Matplotlib是Python中最重要的数据可视化库之一，它提供了类似MATLAB的绘图接口，可以创建高质量的静态、动态和交互式图表。\n", "\n", "### 为什么选择Matplotlib？\n", "- **功能强大**：支持几乎所有类型的图表\n", "- **高度定制**：每个图表元素都可以精确控制\n", "- **广泛应用**：科学计算、数据分析、机器学习的标准工具\n", "- **生态丰富**：与NumPy、Pandas等库完美集成\n", "- **开源免费**：完全开源，社区活跃"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 🔧 安装和导入\n", "\n", "### 安装<PERSON><PERSON><PERSON><PERSON><PERSON>\n", "```bash\n", "# 使用pip安装\n", "pip install matplotlib\n", "\n", "# 使用conda安装\n", "conda install matplotlib\n", "```"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 基本导入语句\n", "import matplotlib.pyplot as plt  # 最常用的导入方式\n", "import numpy as np               # 通常配合使用\n", "\n", "# 检查matplotlib版本\n", "import matplotlib\n", "print(f\"Matplotlib版本: {matplotlib.__version__}\")\n", "\n", "# 设置中文字体支持\n", "plt.rcParams['font.sans-serif'] = ['SimHei', 'DejaVu Sans']  # 支持中文显示\n", "plt.rcParams['axes.unicode_minus'] = False  # 正常显示负号\n", "\n", "print(\"✅ Matplotlib导入成功！\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 🏗️ Mat<PERSON><PERSON>lib核心架构\n", "\n", "### 三层架构理解\n", "1. **Backend Layer（后端层）**：负责实际的图形渲染\n", "2. **Artist Layer（艺术家层）**：所有图形元素的抽象\n", "3. **Scripting Layer（脚本层）**：pyplot接口，最常用的层级\n", "\n", "### 核心概念\n", "- **Figure（画布）**：整个图形窗口\n", "- **Axes（坐标轴）**：图表的绘图区域\n", "- **Axis（轴）**：x轴、y轴等具体的轴\n", "- **Artist（艺术家）**：图形中的所有可见元素"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 创建第一个图表 - 理解Figure和Axes\n", "fig, ax = plt.subplots(figsize=(8, 6))  # 创建画布和坐标轴\n", "\n", "# 简单的数据\n", "x = [1, 2, 3, 4, 5]\n", "y = [2, 4, 6, 8, 10]\n", "\n", "# 绘制线图\n", "ax.plot(x, y, 'bo-', linewidth=2, markersize=8)\n", "\n", "# 添加标题和标签\n", "ax.set_title('我的第一个Matplotlib图表', fontsize=16, fontweight='bold')\n", "ax.set_xlabel('X轴标签', fontsize=12)\n", "ax.set_ylabel('Y轴标签', fontsize=12)\n", "\n", "# 添加网格\n", "ax.grid(True, alpha=0.3)\n", "\n", "# 显示图表\n", "plt.tight_layout()  # 自动调整布局\n", "plt.show()\n", "\n", "print(\"🎉 恭喜！您已经创建了第一个matplotlib图表！\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 📦 Matplotlib主要子包介绍\n", "\n", "### 1. pyplot - 最常用的接口"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# pyplot提供MATLAB风格的接口\n", "plt.figure(figsize=(10, 4))\n", "\n", "# 创建两个子图\n", "plt.subplot(1, 2, 1)\n", "plt.plot([1, 2, 3, 4], [1, 4, 2, 3], 'r-o')\n", "plt.title('子图1 - 线图')\n", "\n", "plt.subplot(1, 2, 2)\n", "plt.bar(['A', 'B', 'C', 'D'], [3, 7, 2, 5], color='skyblue')\n", "plt.title('子图2 - 柱状图')\n", "\n", "plt.tight_layout()\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 2. figure - 画布管理"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from matplotlib.figure import Figure\n", "\n", "# 创建自定义画布\n", "fig = Figure(figsize=(8, 6), facecolor='lightgray')\n", "ax = fig.add_subplot(111)\n", "\n", "# 绘制数据\n", "x = np.linspace(0, 2*np.pi, 100)\n", "y = np.sin(x)\n", "ax.plot(x, y, 'b-', linewidth=2, label='sin(x)')\n", "ax.set_title('正弦函数图')\n", "ax.legend()\n", "ax.grid(True)\n", "\n", "# 显示（在Jupyter中）\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 3. patches - 图形补丁（形状）"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from matplotlib.patches import Rectangle, Circle\n", "\n", "fig, ax = plt.subplots(figsize=(8, 6))\n", "\n", "# 添加矩形\n", "rect = Rectangle((1, 1), 2, 1.5, linewidth=2, edgecolor='red', facecolor='lightblue', alpha=0.7)\n", "ax.add_patch(rect)\n", "\n", "# 添加圆形\n", "circle = Circle((5, 3), 1, linewidth=2, edgecolor='green', facecolor='yellow', alpha=0.7)\n", "ax.add_patch(circle)\n", "\n", "# 设置坐标轴范围\n", "ax.set_xlim(0, 7)\n", "ax.set_ylim(0, 5)\n", "ax.set_aspect('equal')  # 保持长宽比\n", "ax.set_title('使用patches添加几何形状')\n", "ax.grid(True, alpha=0.3)\n", "\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 4. animation - 动画功能预览"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 简单的动画示例（静态展示动画的一帧）\n", "from matplotlib.animation import FuncAnimation\n", "\n", "# 创建数据\n", "x = np.linspace(0, 2*np.pi, 100)\n", "y = np.sin(x)\n", "\n", "fig, ax = plt.subplots(figsize=(10, 6))\n", "line, = ax.plot(x, y, 'b-', linewidth=2)\n", "ax.set_title('动画预览 - 正弦波（静态展示）')\n", "ax.set_xlabel('x')\n", "ax.set_ylabel('sin(x)')\n", "ax.grid(True, alpha=0.3)\n", "\n", "plt.show()\n", "print(\"💡 提示：完整的动画功能将在第26节详细学习\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 🔍 举一反三\n", "\n", "### 场景1：解决中文显示问题\n", "**问题描述**：在matplotlib中显示中文时出现方块或乱码\n", "**解决思路**：配置合适的中文字体\n", "**代码实现**："]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 方法1：全局设置（推荐）\n", "plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'DejaVu Sans']\n", "plt.rcParams['axes.unicode_minus'] = False\n", "\n", "# 测试中文显示\n", "plt.figure(figsize=(8, 6))\n", "plt.plot([1, 2, 3, 4], [1, 4, 2, 3], 'ro-', linewidth=2, markersize=8)\n", "plt.title('中文标题测试 - 数据可视化', fontsize=16)\n", "plt.xlabel('横坐标（时间）', fontsize=12)\n", "plt.ylabel('纵坐标（数值）', fontsize=12)\n", "plt.grid(True, alpha=0.3)\n", "plt.show()\n", "\n", "print(\"✅ 中文显示正常！\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 方法2：临时设置字体\n", "from matplotlib.font_manager import FontProperties\n", "\n", "# 创建字体属性对象\n", "font_prop = FontProperties(fname='C:/Windows/Fonts/simhei.ttf', size=12)\n", "\n", "plt.figure(figsize=(8, 6))\n", "plt.plot([1, 2, 3, 4], [2, 3, 1, 4], 'bs-', linewidth=2)\n", "plt.title('临时字体设置示例', fontproperties=font_prop, fontsize=16)\n", "plt.xlabel('X轴', fontproperties=font_prop)\n", "plt.ylabel('Y轴', fontproperties=font_prop)\n", "plt.show()\n", "\n", "print(\"💡 提示：方法1更简便，推荐使用\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 场景2：创建专业的图表模板\n", "**问题描述**：需要创建统一风格的专业图表\n", "**解决思路**：使用样式设置和模板函数"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def create_professional_plot(x, y, title=\"图表标题\", xlabel=\"X轴\", ylabel=\"Y轴\"):\n", "    \"\"\"\n", "    创建专业风格的图表模板\n", "    \"\"\"\n", "    # 设置图表样式\n", "    plt.style.use('seaborn-v0_8')  # 使用seaborn样式\n", "    \n", "    fig, ax = plt.subplots(figsize=(10, 6))\n", "    \n", "    # 绘制数据\n", "    ax.plot(x, y, linewidth=2.5, marker='o', markersize=6, \n", "            color='#2E86AB', markerfacecolor='#A23B72')\n", "    \n", "    # 设置标题和标签\n", "    ax.set_title(title, fontsize=16, fontweight='bold', pad=20)\n", "    ax.set_xlabel(xlabel, fontsize=12)\n", "    ax.set_ylabel(ylabel, fontsize=12)\n", "    \n", "    # 美化网格\n", "    ax.grid(True, alpha=0.3, linestyle='--')\n", "    \n", "    # 设置背景色\n", "    ax.set_facecolor('#F8F9FA')\n", "    \n", "    # 调整布局\n", "    plt.tight_layout()\n", "    \n", "    return fig, ax\n", "\n", "# 使用模板\n", "x_data = np.linspace(0, 10, 50)\n", "y_data = np.sin(x_data) * np.exp(-x_data/10)\n", "\n", "fig, ax = create_professional_plot(x_data, y_data, \n", "                                  title=\"专业图表模板示例\",\n", "                                  xlabel=\"时间 (秒)\",\n", "                                  ylabel=\"振幅\")\n", "plt.show()\n", "\n", "print(\"🎨 专业图表模板创建成功！\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 💪 动手练习\n", "\n", "### 练习1：基础绘图练习\n", "**题目**：使用提供的数据创建一个基本的线图，要求包含标题、坐标轴标签和网格\n", "\n", "**数据准备**："]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 练习数据\n", "months = ['1月', '2月', '3月', '4月', '5月', '6月']\n", "sales = [120, 135, 148, 162, 178, 195]\n", "\n", "# TODO: 在这里创建你的图表\n", "# 提示：使用 plt.plot() 函数\n", "\n", "# TODO: 添加标题\n", "# 提示：使用 plt.title()\n", "\n", "# TODO: 添加坐标轴标签\n", "# 提示：使用 plt.xlabel() 和 plt.ylabel()\n", "\n", "# TODO: 添加网格\n", "# 提示：使用 plt.grid(True)\n", "\n", "# TODO: 显示图表\n", "# 提示：使用 plt.show()\n", "\n", "print(\"请在上面的代码框中完成练习\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["**参考答案**："]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 参考答案（点击运行查看）\n", "plt.figure(figsize=(10, 6))\n", "plt.plot(months, sales, 'bo-', linewidth=2, markersize=8)\n", "plt.title('月度销售额趋势图', fontsize=16, fontweight='bold')\n", "plt.xlabel('月份', fontsize=12)\n", "plt.ylabel('销售额 (万元)', fontsize=12)\n", "plt.grid(True, alpha=0.3)\n", "plt.xticks(rotation=45)  # 旋转x轴标签\n", "plt.tight_layout()\n", "plt.show()\n", "\n", "print(\"✅ 练习1完成！\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 练习2：错误修正练习\n", "**题目**：下面的代码有几个问题，请找出并修正\n", "\n", "**有问题的代码**："]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 有问题的代码 - 请修正后运行\n", "import matplotlib.pyplot as plt\n", "\n", "data = [1, 2, 3, 4, 5]\n", "plt.plot(data)  # 问题1：缺少x轴数据\n", "plt.title(\"我的图表\")  # 问题2：可能的中文显示问题\n", "# 问题3：缺少坐标轴标签\n", "# 问题4：没有调用plt.show()\n", "\n", "print(\"请修正上面代码中的问题\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["**修正后的代码**："]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 修正后的代码\n", "import matplotlib.pyplot as plt\n", "\n", "# 确保中文显示正常\n", "plt.rcParams['font.sans-serif'] = ['SimHei', 'DejaVu Sans']\n", "plt.rcParams['axes.unicode_minus'] = False\n", "\n", "x_data = [1, 2, 3, 4, 5]  # 添加x轴数据\n", "y_data = [1, 2, 3, 4, 5]\n", "\n", "plt.figure(figsize=(8, 6))\n", "plt.plot(x_data, y_data, 'ro-', linewidth=2)  # 指定x和y数据\n", "plt.title(\"我的图表\", fontsize=14)  # 中文标题\n", "plt.xlabel(\"X轴标签\", fontsize=12)  # 添加x轴标签\n", "plt.ylabel(\"Y轴标签\", fontsize=12)  # 添加y轴标签\n", "plt.grid(True, alpha=0.3)  # 添加网格\n", "plt.show()  # 显示图表\n", "\n", "print(\"✅ 所有问题已修正！\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 🚨 常见问题和解决方案\n", "\n", "### 问题1：中文显示为方块\n", "**错误现象**：图表中的中文标题、标签显示为方块或问号\n", "**原因分析**：系统缺少中文字体或matplotlib未正确配置中文字体\n", "**解决方案**："]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 解决中文显示问题的完整方案\n", "import matplotlib.pyplot as plt\n", "from matplotlib.font_manager import fontManager\n", "\n", "# 查看系统可用字体\n", "fonts = [f.name for f in fontManager.ttflist if 'SimHei' in f.name or 'Microsoft' in f.name]\n", "print(\"系统可用中文字体：\", fonts[:5])  # 显示前5个\n", "\n", "# 设置中文字体（多个备选）\n", "plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'DejaVu Sans']\n", "plt.rcParams['axes.unicode_minus'] = False\n", "\n", "# 测试\n", "plt.figure(figsize=(6, 4))\n", "plt.text(0.5, 0.5, '中文显示测试 - 成功！', fontsize=16, ha='center', va='center')\n", "plt.xlim(0, 1)\n", "plt.ylim(0, 1)\n", "plt.title('中文字体测试')\n", "plt.show()\n", "\n", "print(\"✅ 中文显示配置完成\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 问题2：图表显示不完整\n", "**错误现象**：标签被截断或图表边缘显示不全\n", "**解决方案**："]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 解决图表显示不完整的问题\n", "plt.figure(figsize=(10, 6))\n", "\n", "# 创建一些数据\n", "categories = ['非常长的类别名称A', '非常长的类别名称B', '非常长的类别名称C']\n", "values = [25, 35, 30]\n", "\n", "plt.bar(categories, values)\n", "plt.title('解决标签截断问题示例')\n", "plt.ylabel('数值')\n", "\n", "# 旋转x轴标签\n", "plt.xticks(rotation=45, ha='right')\n", "\n", "# 自动调整布局（重要！）\n", "plt.tight_layout()\n", "\n", "plt.show()\n", "\n", "print(\"💡 使用plt.tight_layout()可以自动调整布局，避免标签截断\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 📚 知识小结\n", "\n", "### 核心要点\n", "- **导入方式**：`import matplotlib.pyplot as plt` 是标准导入方式\n", "- **中文支持**：设置 `plt.rcParams['font.sans-serif']` 和 `plt.rcParams['axes.unicode_minus']`\n", "- **基本结构**：Figure（画布）→ Axes（坐标轴）→ 具体图形元素\n", "- **布局调整**：使用 `plt.tight_layout()` 避免元素重叠\n", "- **样式设置**：可以通过rcParams全局设置，也可以单独设置\n", "\n", "### 下节预告\n", "下一节我们将学习**线图（折线图）**的详细用法，包括：\n", "- 基本线图绘制\n", "- 多条线图对比\n", "- 线型、颜色、标记的设置\n", "- 图例和注释的添加\n", "\n", "## 🌟 扩展阅读\n", "- [Matplotlib官方文档](https://matplotlib.org/stable/)\n", "- [Matplotlib教程](https://matplotlib.org/stable/tutorials/index.html)\n", "- [颜色和样式参考](https://matplotlib.org/stable/gallery/color/named_colors.html)\n", "- [中文字体配置详解](https://matplotlib.org/stable/tutorials/text/text_props.html)"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.5"}}, "nbformat": 4, "nbformat_minor": 4}