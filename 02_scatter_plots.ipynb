{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# 散点图 - 从入门到精通\n", "\n", "## 📖 本节学习目标\n", "- 掌握散点图的基本概念和适用场景\n", "- 学会使用matplotlib创建各种类型的散点图\n", "- 理解散点图的参数设置和美化技巧\n", "- 能够根据数据特点选择合适的散点图变体\n", "- 掌握多维数据在散点图中的表示方法"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 导入必要的库\n", "import matplotlib.pyplot as plt\n", "import numpy as np\n", "import pandas as pd\n", "from matplotlib.colors import ListedColormap\n", "\n", "# 设置中文字体\n", "plt.rcParams['font.sans-serif'] = ['SimHei', 'DejaVu Sans']\n", "plt.rcParams['axes.unicode_minus'] = False\n", "\n", "# 设置图表样式\n", "plt.style.use('default')\n", "\n", "print(\"✅ 环境配置完成！\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 🎯 知识点详解\n", "\n", "### 1. 散点图概念介绍\n", "\n", "**定义和特点**：\n", "- 散点图使用点的位置来表示两个连续变量之间的关系\n", "- 每个点代表一个观测值，横坐标和纵坐标分别对应两个变量的值\n", "- 是探索变量间相关性的最直观工具\n", "- 可以轻松识别异常值、聚类和趋势模式\n", "\n", "**适用数据类型**：\n", "- 两个或多个连续型数值变量\n", "- 可以通过颜色、大小、形状编码额外的分类或数值信息\n", "- 适合中小规模数据集（过多数据点会造成重叠）\n", "\n", "**使用场景分析**：\n", "- 探索两个变量之间的相关关系\n", "- 识别数据中的异常值或离群点\n", "- 展示数据的分布模式和聚类情况\n", "- 比较不同组别数据的分布差异\n", "\n", "**与其他图表的区别**：\n", "- 与线图：散点图不连接数据点，更适合展示离散的观测值\n", "- 与柱状图：散点图用于连续变量，柱状图用于分类变量\n", "- 与热力图：散点图保留原始数据点信息，热力图显示聚合后的密度"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 2. 基础语法和参数\n", "\n", "**核心函数介绍**：\n", "- `plt.scatter(x, y, ...)` - 创建散点图的主要函数\n", "- `ax.scatter(x, y, ...)` - 面向对象接口的散点图函数\n", "\n", "**必需参数详解**：\n", "- `x`: 横坐标数据，必须是数值型数组或列表\n", "- `y`: 纵坐标数据，必须是数值型数组或列表，长度与x相同\n", "\n", "**重要可选参数说明**：\n", "- `s`: 点的大小，可以是标量或数组（用于气泡图）\n", "- `c`: 点的颜色，可以是颜色名称、RGB值或数组（用于颜色映射）\n", "- `marker`: 点的形状，如'o'（圆形）、's'（方形）、'^'（三角形）等\n", "- `alpha`: 透明度，0-1之间的浮点数\n", "- `cmap`: 颜色映射，当c为数值数组时使用\n", "- `edgecolors`: 点的边框颜色\n", "- `linewidths`: 点的边框宽度\n", "\n", "**返回值说明**：\n", "- 返回PathCollection对象，可用于后续的颜色条添加和属性修改"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 3. 代码演示（从简单到复杂）\n", "\n", "#### 3.1 最简单示例"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 创建基础散点图\n", "# 生成示例数据\n", "np.random.seed(42)  # 设置随机种子，确保结果可重现\n", "x = np.random.randn(50)  # 50个随机数作为x坐标\n", "y = 2 * x + np.random.randn(50)  # y与x有线性关系，加上噪声\n", "\n", "# 创建最简单的散点图\n", "plt.figure(figsize=(8, 6))\n", "plt.scatter(x, y)  # 使用默认参数创建散点图\n", "plt.title('最简单的散点图示例')  # 添加标题\n", "plt.xlabel('X变量')  # x轴标签\n", "plt.ylabel('Y变量')  # y轴标签\n", "plt.grid(True, alpha=0.3)  # 添加网格，透明度0.3\n", "plt.show()\n", "\n", "print(\"✅ 基础散点图创建完成！\")\n", "print(f\"数据点数量: {len(x)}\")\n", "print(f\"X的范围: [{x.min():.2f}, {x.max():.2f}]\")\n", "print(f\"Y的范围: [{y.min():.2f}, {y.max():.2f}]\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### 3.2 参数定制示例"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 展示各种参数的效果\n", "fig, axes = plt.subplots(2, 2, figsize=(12, 10))\n", "fig.suptitle('散点图参数定制示例', fontsize=16, fontweight='bold')\n", "\n", "# 生成数据\n", "np.random.seed(42)\n", "x = np.random.randn(100)\n", "y = 1.5 * x + np.random.randn(100) * 0.5\n", "\n", "# 子图1：不同颜色和大小\n", "axes[0, 0].scatter(x, y, c='red', s=50, alpha=0.7)\n", "axes[0, 0].set_title('颜色和大小设置')\n", "axes[0, 0].set_xlabel('X变量')\n", "axes[0, 0].set_ylabel('Y变量')\n", "axes[0, 0].grid(True, alpha=0.3)\n", "\n", "# 子图2：不同形状的点\n", "markers = ['o', 's', '^', 'D', 'v']  # 圆形、方形、上三角、菱形、下三角\n", "colors = ['blue', 'green', 'red', 'purple', 'orange']\n", "for i, (marker, color) in enumerate(zip(markers, colors)):\n", "    # 每种形状显示20个点\n", "    start_idx = i * 20\n", "    end_idx = (i + 1) * 20\n", "    axes[0, 1].scatter(x[start_idx:end_idx], y[start_idx:end_idx], \n", "                      marker=marker, c=color, s=60, alpha=0.8, \n", "                      label=f'类型{i+1}')\n", "axes[0, 1].set_title('不同点形状')\n", "axes[0, 1].set_xlabel('X变量')\n", "axes[0, 1].set_ylabel('Y变量')\n", "axes[0, 1].legend()\n", "axes[0, 1].grid(True, alpha=0.3)\n", "\n", "# 子图3：透明度效果\n", "axes[1, 0].scatter(x, y, c='blue', s=100, alpha=0.3, label='透明度0.3')\n", "axes[1, 0].scatter(x + 0.5, y + 0.5, c='red', s=100, alpha=0.8, label='透明度0.8')\n", "axes[1, 0].set_title('透明度对比')\n", "axes[1, 0].set_xlabel('X变量')\n", "axes[1, 0].set_ylabel('Y变量')\n", "axes[1, 0].legend()\n", "axes[1, 0].grid(True, alpha=0.3)\n", "\n", "# 子图4：边框设置\n", "axes[1, 1].scatter(x, y, c='lightblue', s=80, \n", "                  edgecolors='darkblue', linewidths=2, alpha=0.8)\n", "axes[1, 1].set_title('带边框的散点')\n", "axes[1, 1].set_xlabel('X变量')\n", "axes[1, 1].set_ylabel('Y变量')\n", "axes[1, 1].grid(True, alpha=0.3)\n", "\n", "plt.tight_layout()\n", "plt.show()\n", "\n", "print(\"🎨 参数定制示例完成！\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### 3.3 美化和进阶示例"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 高级定制效果 - 气泡图和颜色映射\n", "fig, axes = plt.subplots(1, 2, figsize=(15, 6))\n", "fig.suptitle('高级散点图技巧', fontsize=16, fontweight='bold')\n", "\n", "# 生成多维数据\n", "np.random.seed(42)\n", "n_points = 100\n", "x = np.random.randn(n_points)\n", "y = 1.2 * x + np.random.randn(n_points) * 0.8\n", "sizes = np.random.randint(20, 200, n_points)  # 随机大小，用于气泡图\n", "colors = x + y  # 颜色基于x+y的值\n", "\n", "# 子图1：气泡图（大小编码第三维信息）\n", "scatter1 = axes[0].scatter(x, y, s=sizes, c=colors, \n", "                          cmap='viridis', alpha=0.7, \n", "                          edgecolors='black', linewidths=0.5)\n", "axes[0].set_title('气泡图 - 大小表示第三维数据')\n", "axes[0].set_xlabel('X变量')\n", "axes[0].set_ylabel('Y变量')\n", "axes[0].grid(True, alpha=0.3)\n", "\n", "# 添加颜色条\n", "cbar1 = plt.colorbar(scatter1, ax=axes[0])\n", "cbar1.set_label('颜色值 (X+Y)', rotation=270, labelpad=15)\n", "\n", "# 子图2：分类散点图\n", "# 创建三个不同的类别\n", "np.random.seed(42)\n", "n_per_class = 50\n", "classes = ['类别A', '类别B', '类别C']\n", "colors_cat = ['#FF6B6B', '#4ECDC4', '#45B7D1']\n", "markers_cat = ['o', 's', '^']\n", "\n", "for i, (class_name, color, marker) in enumerate(zip(classes, colors_cat, markers_cat)):\n", "    # 为每个类别生成不同分布的数据\n", "    x_class = np.random.randn(n_per_class) + i * 2  # 不同的中心位置\n", "    y_class = np.random.randn(n_per_class) + i * 1.5\n", "    \n", "    axes[1].scatter(x_class, y_class, c=color, marker=marker, \n", "                   s=80, alpha=0.8, label=class_name, \n", "                   edgecolors='white', linewidths=1)\n", "\n", "axes[1].set_title('分类散点图 - 不同类别用不同颜色和形状')\n", "axes[1].set_xlabel('X变量')\n", "axes[1].set_ylabel('Y变量')\n", "axes[1].legend(title='数据类别', loc='upper left')\n", "axes[1].grid(True, alpha=0.3)\n", "\n", "plt.tight_layout()\n", "plt.show()\n", "\n", "print(\"🌟 高级散点图技巧展示完成！\")\n", "print(\"💡 提示：气泡图可以同时展示4个维度的信息（x, y, 大小, 颜色）\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 专业级散点图 - 添加趋势线和置信区间\n", "from scipy import stats\n", "from sklearn.linear_model import LinearRegression\n", "from sklearn.preprocessing import PolynomialFeatures\n", "\n", "# 生成数据\n", "np.random.seed(42)\n", "x = np.linspace(0, 10, 100)\n", "y = 2 * x + 1 + np.random.normal(0, 2, 100)  # 线性关系加噪声\n", "\n", "plt.figure(figsize=(12, 8))\n", "\n", "# 创建散点图\n", "plt.scatter(x, y, alpha=0.6, s=50, color='#2E86AB', \n", "           edgecolors='white', linewidths=0.5, label='数据点')\n", "\n", "# 添加线性趋势线\n", "slope, intercept, r_value, p_value, std_err = stats.linregress(x, y)\n", "line = slope * x + intercept\n", "plt.plot(x, line, 'r-', linewidth=2, label=f'趋势线 (R²={r_value**2:.3f})')\n", "\n", "# 添加置信区间（简化版）\n", "residuals = y - line\n", "std_residuals = np.std(residuals)\n", "plt.fill_between(x, line - 1.96*std_residuals, line + 1.96*std_residuals, \n", "                alpha=0.2, color='red', label='95%置信区间')\n", "\n", "# 美化图表\n", "plt.title('专业散点图 - 带趋势线和置信区间', fontsize=16, fontweight='bold', pad=20)\n", "plt.xlabel('X变量', fontsize=12)\n", "plt.ylabel('Y变量', fontsize=12)\n", "plt.legend(loc='upper left', frameon=True, fancybox=True, shadow=True)\n", "plt.grid(True, alpha=0.3, linestyle='--')\n", "\n", "# 添加统计信息文本\n", "stats_text = f'斜率: {slope:.2f}\\n截距: {intercept:.2f}\\nR²: {r_value**2:.3f}\\np值: {p_value:.2e}'\n", "plt.text(0.02, 0.98, stats_text, transform=plt.gca().transAxes, \n", "         verticalalignment='top', bbox=dict(boxstyle='round', facecolor='wheat', alpha=0.8))\n", "\n", "plt.tight_layout()\n", "plt.show()\n", "\n", "print(\"📊 专业级散点图创建完成！\")\n", "print(f\"相关系数: {r_value:.3f}\")\n", "print(f\"决定系数 R²: {r_value**2:.3f}\")\n", "print(f\"p值: {p_value:.2e}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 🔍 举一反三\n", "\n", "### 场景1：房价分析 - 探索房屋面积与价格的关系\n", "**问题描述**：房地产公司需要分析房屋面积与售价之间的关系，同时考虑房屋类型（公寓、别墅、联排）的影响\n", "**解决思路**：使用散点图展示面积与价格的关系，用不同颜色和形状区分房屋类型，用点的大小表示房龄\n", "**代码实现**："]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 房价分析散点图\n", "np.random.seed(42)\n", "\n", "# 模拟房屋数据\n", "n_houses = 200\n", "house_types = ['公寓', '别墅', '联排']\n", "type_colors = ['#FF6B6B', '#4ECDC4', '#45B7D1']\n", "type_markers = ['o', 's', '^']\n", "\n", "plt.figure(figsize=(14, 8))\n", "\n", "for i, (house_type, color, marker) in enumerate(zip(house_types, type_colors, type_markers)):\n", "    # 为不同类型的房屋生成不同的数据分布\n", "    n_type = n_houses // 3\n", "    \n", "    if house_type == '公寓':\n", "        area = np.random.normal(80, 20, n_type)  # 面积较小\n", "        base_price = 8000  # 基础单价\n", "    elif house_type == '别墅':\n", "        area = np.random.normal(200, 50, n_type)  # 面积较大\n", "        base_price = 15000  # 基础单价较高\n", "    else:  # 联排\n", "        area = np.random.normal(140, 30, n_type)  # 中等面积\n", "        base_price = 12000  # 中等单价\n", "    \n", "    # 确保面积为正数\n", "    area = np.maximum(area, 30)\n", "    \n", "    # 价格与面积相关，加上随机噪声\n", "    price = base_price * area + np.random.normal(0, area * 500, n_type)\n", "    price = np.maximum(price, 100000)  # 确保价格为正\n", "    \n", "    # 房龄（用于控制点的大小）\n", "    age = np.random.randint(1, 30, n_type)\n", "    sizes = 200 - age * 5  # 房龄越大，点越小\n", "    \n", "    # 绘制散点图\n", "    scatter = plt.scatter(area, price/10000, s=sizes, c=color, marker=marker, \n", "                         alpha=0.7, edgecolors='white', linewidths=1, \n", "                         label=f'{house_type} (n={n_type})')\n", "\n", "# 图表美化\n", "plt.title('房屋面积与价格关系分析', fontsize=16, fontweight='bold', pad=20)\n", "plt.xlabel('房屋面积 (平方米)', fontsize=12)\n", "plt.ylabel('房屋价格 (万元)', fontsize=12)\n", "plt.legend(title='房屋类型', loc='upper left', frameon=True, fancybox=True)\n", "plt.grid(True, alpha=0.3, linestyle='--')\n", "\n", "# 添加说明文字\n", "plt.text(0.98, 0.02, '注：点的大小表示房龄\\n(点越大房龄越小)', \n", "         transform=plt.gca().transAxes, ha='right', va='bottom',\n", "         bbox=dict(boxstyle='round', facecolor='lightgray', alpha=0.8))\n", "\n", "plt.tight_layout()\n", "plt.show()\n", "\n", "print(\"🏠 房价分析散点图完成！\")\n", "print(\"💡 从图中可以看出：\")\n", "print(\"   - 别墅面积大、单价高\")\n", "print(\"   - 公寓面积小、相对便宜\")\n", "print(\"   - 联排介于两者之间\")\n", "print(\"   - 点的大小反映了房龄信息\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["**知识扩展**：\n", "- 多维信息编码：位置(x,y) + 颜色(类别) + 大小(数值) + 形状(类别)\n", "- 数据预处理：确保数据的合理性（非负值、异常值处理）\n", "- 视觉层次：通过透明度、边框等增强可读性"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 场景2：学生成绩分析 - 多科目成绩相关性探索\n", "**问题描述**：教育部门需要分析学生数学和英语成绩的相关性，同时考虑不同班级和性别的差异\n", "**解决思路**：创建交互式散点图，用颜色区分班级，用形状区分性别，添加回归线分析相关性\n", "**代码实现**："]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 学生成绩分析散点图\n", "np.random.seed(42)\n", "\n", "# 模拟学生数据\n", "n_students = 150\n", "classes = ['一班', '二班', '三班']\n", "genders = ['男', '女']\n", "class_colors = ['#FF6B6B', '#4ECDC4', '#45B7D1']\n", "gender_markers = {'男': 'o', '女': 's'}\n", "\n", "fig, axes = plt.subplots(1, 2, figsize=(16, 6))\n", "fig.suptitle('学生成绩相关性分析', fontsize=16, fontweight='bold')\n", "\n", "all_math_scores = []\n", "all_english_scores = []\n", "\n", "# 子图1：按班级分类\n", "for i, (class_name, color) in enumerate(zip(classes, class_colors)):\n", "    n_class = n_students // 3\n", "    \n", "    # 不同班级有不同的成绩分布\n", "    math_mean = 75 + i * 5  # 一班75分，二班80分，三班85分\n", "    english_mean = 70 + i * 3\n", "    \n", "    math_scores = np.random.normal(math_mean, 12, n_class)\n", "    # 英语成绩与数学成绩有一定相关性\n", "    english_scores = 0.6 * math_scores + np.random.normal(english_mean - 0.6 * math_mean, 8, n_class)\n", "    \n", "    # 确保成绩在合理范围内\n", "    math_scores = np.clip(math_scores, 0, 100)\n", "    english_scores = np.clip(english_scores, 0, 100)\n", "    \n", "    all_math_scores.extend(math_scores)\n", "    all_english_scores.extend(english_scores)\n", "    \n", "    axes[0].scatter(math_scores, english_scores, c=color, s=60, alpha=0.7, \n", "                   edgecolors='white', linewidths=0.5, label=class_name)\n", "\n", "# 添加整体回归线\n", "all_math_scores = np.array(all_math_scores)\n", "all_english_scores = np.array(all_english_scores)\n", "slope, intercept, r_value, _, _ = stats.linregress(all_math_scores, all_english_scores)\n", "line = slope * all_math_scores + intercept\n", "axes[0].plot(all_math_scores, line, 'r--', linewidth=2, alpha=0.8, \n", "            label=f'回归线 (R={r_value:.3f})')\n", "\n", "axes[0].set_title('按班级分类的成绩分布')\n", "axes[0].set_xlabel('数学成绩')\n", "axes[0].set_ylabel('英语成绩')\n", "axes[0].legend()\n", "axes[0].grid(True, alpha=0.3)\n", "axes[0].set_xlim(40, 100)\n", "axes[0].set_ylim(40, 100)\n", "\n", "# 子图2：按性别分类\n", "np.random.seed(42)\n", "for gender in genders:\n", "    n_gender = n_students // 2\n", "    \n", "    if gender == '男':\n", "        math_scores = np.random.normal(78, 15, n_gender)\n", "        english_scores = 0.5 * math_scores + np.random.normal(35, 10, n_gender)\n", "        color = '#4A90E2'\n", "    else:\n", "        math_scores = np.random.normal(82, 12, n_gender)\n", "        english_scores = 0.7 * math_scores + np.random.normal(25, 8, n_gender)\n", "        color = '#E24A90'\n", "    \n", "    math_scores = np.clip(math_scores, 0, 100)\n", "    english_scores = np.clip(english_scores, 0, 100)\n", "    \n", "    axes[1].scatter(math_scores, english_scores, c=color, \n", "                   marker=gender_markers[gender], s=60, alpha=0.7,\n", "                   edgecolors='white', linewidths=0.5, label=gender)\n", "\n", "axes[1].set_title('按性别分类的成绩分布')\n", "axes[1].set_xlabel('数学成绩')\n", "axes[1].set_ylabel('英语成绩')\n", "axes[1].legend()\n", "axes[1].grid(True, alpha=0.3)\n", "axes[1].set_xlim(40, 100)\n", "axes[1].set_ylim(40, 100)\n", "\n", "plt.tight_layout()\n", "plt.show()\n", "\n", "print(\"📚 学生成绩分析完成！\")\n", "print(f\"数学与英语成绩相关系数: {r_value:.3f}\")\n", "print(\"💡 分析结果：\")\n", "print(\"   - 数学和英语成绩呈正相关\")\n", "print(\"   - 不同班级成绩水平有差异\")\n", "print(\"   - 性别在成绩分布上可能存在差异\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["**知识扩展**：\n", "- 相关性分析：使用scipy.stats.linregress计算相关系数\n", "- 分组比较：通过颜色和形状同时编码两个分类变量\n", "- 数据范围控制：使用np.clip确保数据在合理范围内"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 场景3：股票市场分析 - 风险与收益的可视化\n", "**问题描述**：投资分析师需要展示不同股票的风险（波动率）与收益率的关系，同时显示市值信息\n", "**解决思路**：创建风险-收益散点图，用气泡大小表示市值，用颜色表示行业分类，添加有效前沿线\n", "**代码实现**："]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 股票风险收益分析\n", "np.random.seed(42)\n", "\n", "# 模拟股票数据\n", "n_stocks = 80\n", "industries = ['科技', '金融', '医药', '消费', '能源']\n", "industry_colors = ['#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7']\n", "\n", "plt.figure(figsize=(14, 10))\n", "\n", "all_risk = []\n", "all_return = []\n", "all_market_cap = []\n", "\n", "for i, (industry, color) in enumerate(zip(industries, industry_colors)):\n", "    n_industry = n_stocks // 5\n", "    \n", "    # 不同行业有不同的风险收益特征\n", "    if industry == '科技':\n", "        base_return = 0.12  # 高收益\n", "        base_risk = 0.25    # 高风险\n", "    elif industry == '金融':\n", "        base_return = 0.08\n", "        base_risk = 0.20\n", "    elif industry == '医药':\n", "        base_return = 0.10\n", "        base_risk = 0.18\n", "    elif industry == '消费':\n", "        base_return = 0.09\n", "        base_risk = 0.15\n", "    else:  # 能源\n", "        base_return = 0.06\n", "        base_risk = 0.30\n", "    \n", "    # 生成风险和收益数据\n", "    risk = np.random.normal(base_risk, 0.05, n_industry)\n", "    returns = np.random.normal(base_return, 0.03, n_industry)\n", "    \n", "    # 确保风险为正\n", "    risk = np.maximum(risk, 0.05)\n", "    \n", "    # 市值（用于气泡大小）\n", "    market_cap = np.random.lognormal(10, 1, n_industry)  # 对数正态分布\n", "    \n", "    all_risk.extend(risk)\n", "    all_return.extend(returns)\n", "    all_market_cap.extend(market_cap)\n", "    \n", "    # 绘制散点图\n", "    sizes = (market_cap - market_cap.min()) / (market_cap.max() - market_cap.min()) * 300 + 50\n", "    plt.scatter(risk, returns, s=sizes, c=color, alpha=0.7, \n", "               edgecolors='white', linewidths=1, label=industry)\n", "\n", "# 添加有效前沿线（简化版）\n", "risk_range = np.linspace(0.1, 0.4, 100)\n", "efficient_frontier = 0.05 + 0.3 * risk_range - 0.2 * risk_range**2\n", "plt.plot(risk_range, efficient_frontier, 'k--', linewidth=2, alpha=0.8, \n", "         label='有效前沿（理论）')\n", "\n", "# 图表美化\n", "plt.title('股票风险-收益分析图', fontsize=16, fontweight='bold', pad=20)\n", "plt.xlabel('风险（年化波动率）', fontsize=12)\n", "plt.ylabel('预期收益率', fontsize=12)\n", "plt.legend(title='行业分类', loc='upper left', frameon=True, fancybox=True)\n", "plt.grid(True, alpha=0.3, linestyle='--')\n", "\n", "# 格式化坐标轴为百分比\n", "plt.gca().xaxis.set_major_formatter(plt.FuncFormatter(lambda x, p: f'{x:.0%}'))\n", "plt.gca().yaxis.set_major_formatter(plt.FuncFormatter(lambda x, p: f'{x:.0%}'))\n", "\n", "# 添加说明\n", "plt.text(0.02, 0.98, '注：气泡大小表示市值\\n虚线为理论有效前沿', \n", "         transform=plt.gca().transAxes, va='top',\n", "         bbox=dict(boxstyle='round', facecolor='lightgray', alpha=0.8))\n", "\n", "plt.tight_layout()\n", "plt.show()\n", "\n", "print(\"📈 股票风险收益分析完成！\")\n", "print(\"💡 投资洞察：\")\n", "print(\"   - 科技股：高风险高收益\")\n", "print(\"   - 消费股：相对稳健\")\n", "print(\"   - 能源股：高风险低收益\")\n", "print(\"   - 理想投资组合应接近有效前沿\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["**知识扩展**：\n", "- 金融可视化：风险-收益散点图是投资分析的经典工具\n", "- 气泡图应用：同时展示三个数值维度（风险、收益、市值）\n", "- 坐标轴格式化：使用FuncFormatter将小数转换为百分比显示\n", "- 理论曲线：添加有效前沿等理论参考线增强分析价值"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 💪 动手练习\n", "\n", "### 练习1：基础散点图练习\n", "**题目**：使用提供的数据创建一个基本的散点图，要求包含标题、坐标轴标签和网格\n", "\n", "**数据准备**："]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 练习数据 - 员工工作年限与薪资关系\n", "np.random.seed(123)\n", "work_years = np.random.uniform(0, 15, 50)  # 工作年限：0-15年\n", "salary = 5000 + work_years * 800 + np.random.normal(0, 1000, 50)  # 薪资与年限相关\n", "salary = np.maximum(salary, 3000)  # 确保薪资不低于3000\n", "\n", "print(\"数据已准备完成：\")\n", "print(f\"工作年限范围：{work_years.min():.1f} - {work_years.max():.1f} 年\")\n", "print(f\"薪资范围：{salary.min():.0f} - {salary.max():.0f} 元\")\n", "print(\"\\n请在下面创建散点图：\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# TODO: 在这里创建你的散点图\n", "# 提示：使用 plt.scatter(work_years, salary)\n", "\n", "# TODO: 添加标题\n", "# 提示：使用 plt.title('员工工作年限与薪资关系')\n", "\n", "# TODO: 添加坐标轴标签\n", "# 提示：使用 plt.xlabel('工作年限（年）') 和 plt.ylabel('薪资（元）')\n", "\n", "# TODO: 添加网格\n", "# 提示：使用 plt.grid(True, alpha=0.3)\n", "\n", "# TODO: 显示图表\n", "# 提示：使用 plt.show()\n", "\n", "print(\"请在上面的代码框中完成练习\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["**期望结果**：创建一个显示工作年限与薪资正相关关系的散点图，包含完整的标题和标签"]}, {"cell_type": "markdown", "metadata": {}, "source": ["**参考答案**："]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 参考答案（点击运行查看）\n", "plt.figure(figsize=(10, 6))\n", "plt.scatter(work_years, salary, c='steelblue', s=60, alpha=0.7, \n", "           edgecolors='white', linewidths=0.5)\n", "plt.title('员工工作年限与薪资关系', fontsize=14, fontweight='bold')\n", "plt.xlabel('工作年限（年）', fontsize=12)\n", "plt.ylabel('薪资（元）', fontsize=12)\n", "plt.grid(True, alpha=0.3)\n", "\n", "# 添加趋势线（额外加分项）\n", "slope, intercept, r_value, _, _ = stats.linregress(work_years, salary)\n", "line = slope * work_years + intercept\n", "plt.plot(work_years, line, 'r--', alpha=0.8, \n", "         label=f'趋势线 (R²={r_value**2:.3f})')\n", "plt.legend()\n", "\n", "plt.tight_layout()\n", "plt.show()\n", "\n", "print(\"✅ 练习1完成！\")\n", "print(f\"相关系数：{r_value:.3f}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 练习2：进阶练习 - 修正错误并完善功能\n", "**题目**：下面的代码有几个问题，请找出并修正，然后按要求添加额外功能\n", "\n", "**有问题的代码**："]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 有问题的代码 - 请修正后运行\n", "import matplotlib.pyplot as plt\n", "import numpy as np\n", "\n", "# 数据\n", "x = [1, 2, 3, 4, 5]\n", "y = [2, 4, 1, 5, 3]\n", "categories = ['A', 'B', 'A', 'B', 'A']\n", "\n", "# 问题1：没有设置图形大小\n", "plt.scatter(x, y)  # 问题2：没有利用categories信息\n", "plt.title(\"散点图示例\")  # 问题3：可能的中文显示问题\n", "# 问题4：缺少坐标轴标签\n", "# 问题5：没有图例\n", "# 问题6：没有调用plt.show()\n", "\n", "print(\"请修正上面代码中的问题，并添加以下功能：\")\n", "print(\"1. 用不同颜色表示不同类别\")\n", "print(\"2. 添加图例\")\n", "print(\"3. 设置点的大小和透明度\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["**修正后的代码**："]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 修正后的代码\n", "import matplotlib.pyplot as plt\n", "import numpy as np\n", "\n", "# 确保中文显示正常\n", "plt.rcParams['font.sans-serif'] = ['SimHei', 'DejaVu Sans']\n", "plt.rcParams['axes.unicode_minus'] = False\n", "\n", "# 数据\n", "x = [1, 2, 3, 4, 5]\n", "y = [2, 4, 1, 5, 3]\n", "categories = ['A', 'B', 'A', 'B', 'A']\n", "\n", "# 设置图形大小\n", "plt.figure(figsize=(10, 6))\n", "\n", "# 按类别分别绘制\n", "unique_categories = list(set(categories))\n", "colors = ['#FF6B6B', '#4ECDC4']\n", "\n", "for i, category in enumerate(unique_categories):\n", "    # 筛选当前类别的数据\n", "    mask = [cat == category for cat in categories]\n", "    x_cat = [x[j] for j in range(len(x)) if mask[j]]\n", "    y_cat = [y[j] for j in range(len(y)) if mask[j]]\n", "    \n", "    plt.scatter(x_cat, y_cat, c=colors[i], s=100, alpha=0.7, \n", "               edgecolors='white', linewidths=2, label=f'类别{category}')\n", "\n", "plt.title('散点图示例 - 按类别分色', fontsize=14, fontweight='bold')\n", "plt.xlabel('X变量', fontsize=12)\n", "plt.ylabel('Y变量', fontsize=12)\n", "plt.legend(title='数据类别', frameon=True, fancybox=True)\n", "plt.grid(True, alpha=0.3)\n", "plt.show()\n", "\n", "print(\"✅ 所有问题已修正并添加了额外功能！\")\n", "print(\"修正内容：\")\n", "print(\"1. ✓ 设置了图形大小\")\n", "print(\"2. ✓ 用不同颜色表示不同类别\")\n", "print(\"3. ✓ 解决了中文显示问题\")\n", "print(\"4. ✓ 添加了坐标轴标签\")\n", "print(\"5. ✓ 添加了图例\")\n", "print(\"6. ✓ 调用了plt.show()\")\n", "print(\"7. ✓ 设置了点的大小、透明度和边框\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 练习3：综合应用 - 创建多维数据可视化\n", "**题目**：基于给定的销售数据，创建一个综合性的散点图分析\n", "\n", "**数据背景**：某公司的产品销售数据，包含广告投入、销售额、产品类型和市场区域信息"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 综合练习数据\n", "np.random.seed(42)\n", "n_products = 100\n", "\n", "# 生成销售数据\n", "ad_spend = np.random.uniform(1000, 50000, n_products)  # 广告投入\n", "sales = 2 * ad_spend + np.random.normal(0, 5000, n_products)  # 销售额与广告投入相关\n", "sales = np.maximum(sales, 1000)  # 确保销售额为正\n", "\n", "product_types = np.random.choice(['电子产品', '服装', '食品'], n_products)\n", "regions = np.random.choice(['北区', '南区', '东区', '西区'], n_products)\n", "profit_margin = np.random.uniform(0.1, 0.4, n_products)  # 利润率\n", "\n", "print(\"销售数据已生成：\")\n", "print(f\"产品数量：{n_products}\")\n", "print(f\"广告投入范围：{ad_spend.min():.0f} - {ad_spend.max():.0f} 元\")\n", "print(f\"销售额范围：{sales.min():.0f} - {sales.max():.0f} 元\")\n", "print(f\"产品类型：{list(set(product_types))}\")\n", "print(f\"市场区域：{list(set(regions))}\")\n", "print(\"\\n请创建一个综合分析图表，要求：\")\n", "print(\"1. 展示广告投入与销售额的关系\")\n", "print(\"2. 用颜色区分产品类型\")\n", "print(\"3. 用点的大小表示利润率\")\n", "print(\"4. 添加趋势线\")\n", "print(\"5. 包含完整的图例和标签\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["**参考答案**："]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 综合练习参考答案\n", "plt.figure(figsize=(14, 8))\n", "\n", "# 产品类型颜色映射\n", "type_colors = {'电子产品': '#FF6B6B', '服装': '#4ECDC4', '食品': '#45B7D1'}\n", "\n", "# 按产品类型分别绘制\n", "for product_type in set(product_types):\n", "    mask = product_types == product_type\n", "    \n", "    # 筛选当前类型的数据\n", "    x_type = ad_spend[mask]\n", "    y_type = sales[mask]\n", "    sizes_type = profit_margin[mask] * 500 + 50  # 利润率转换为点大小\n", "    \n", "    plt.scatter(x_type, y_type, c=type_colors[product_type], \n", "               s=sizes_type, alpha=0.7, edgecolors='white', \n", "               linewidths=1, label=product_type)\n", "\n", "# 添加整体趋势线\n", "slope, intercept, r_value, _, _ = stats.linregress(ad_spend, sales)\n", "line = slope * ad_spend + intercept\n", "plt.plot(ad_spend, line, 'k--', linewidth=2, alpha=0.8, \n", "         label=f'趋势线 (R²={r_value**2:.3f})')\n", "\n", "# 图表美化\n", "plt.title('产品销售分析 - 广告投入与销售额关系', fontsize=16, fontweight='bold', pad=20)\n", "plt.xlabel('广告投入 (元)', fontsize=12)\n", "plt.ylabel('销售额 (元)', fontsize=12)\n", "\n", "# 创建两个图例：一个用于产品类型，一个用于点大小说明\n", "legend1 = plt.legend(title='产品类型', loc='upper left', frameon=True, fancybox=True)\n", "plt.gca().add_artist(legend1)  # 保持第一个图例\n", "\n", "# 添加点大小说明\n", "sizes_legend = [50, 100, 150]\n", "labels_legend = ['低利润率', '中利润率', '高利润率']\n", "for size, label in zip(sizes_legend, labels_legend):\n", "    plt.scatter([], [], s=size, c='gray', alpha=0.7, \n", "               edgecolors='white', linewidths=1, label=label)\n", "plt.legend(title='利润率（点大小）', loc='lower right', frameon=True, fancybox=True)\n", "\n", "plt.grid(True, alpha=0.3, linestyle='--')\n", "\n", "# 添加统计信息\n", "stats_text = f'样本数量: {n_products}\\n相关系数: {r_value:.3f}\\n决定系数: {r_value**2:.3f}'\n", "plt.text(0.02, 0.98, stats_text, transform=plt.gca().transAxes, \n", "         verticalalignment='top', bbox=dict(boxstyle='round', facecolor='wheat', alpha=0.8))\n", "\n", "plt.tight_layout()\n", "plt.show()\n", "\n", "print(\"🎯 综合练习完成！\")\n", "print(\"分析结果：\")\n", "print(f\"- 广告投入与销售额呈强正相关 (R={r_value:.3f})\")\n", "print(f\"- 不同产品类型分布有差异\")\n", "print(f\"- 利润率通过点大小直观展示\")\n", "print(f\"- 该模型可解释销售额变异的 {r_value**2:.1%}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["**思考题**：\n", "1. 为什么选择散点图而不是柱状图来展示这些数据？\n", "2. 如果数据量增加到10万个点，你会如何调整可视化策略？\n", "3. 如何让图表更有说服力？\n", "\n", "**评价标准**：\n", "- 代码正确性：能够正确运行并生成图表\n", "- 图表美观度：颜色搭配、布局合理\n", "- 创新性：是否有独特的展示方式\n", "- 实用性：图表是否能有效传达信息"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 🚨 常见问题和解决方案\n", "\n", "### 问题1：数据点重叠严重，无法看清分布\n", "**错误现象**：当数据点过多或分布集中时，点重叠严重，影响可读性\n", "**原因分析**：数据密度过高，默认的点大小和透明度设置不合适\n", "**解决方案**："]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 解决数据点重叠问题\n", "np.random.seed(42)\n", "# 生成大量重叠数据\n", "x_overlap = np.random.normal(5, 1, 1000)\n", "y_overlap = np.random.normal(5, 1, 1000)\n", "\n", "fig, axes = plt.subplots(1, 3, figsize=(15, 5))\n", "fig.suptitle('解决数据点重叠问题', fontsize=14, fontweight='bold')\n", "\n", "# 问题示例：重叠严重\n", "axes[0].scatter(x_overlap, y_overlap, s=50)\n", "axes[0].set_title('问题：重叠严重')\n", "axes[0].grid(True, alpha=0.3)\n", "\n", "# 解决方案1：调整透明度和大小\n", "axes[1].scatter(x_overlap, y_overlap, s=20, alpha=0.3)\n", "axes[1].set_title('解决方案1：小点+透明度')\n", "axes[1].grid(True, alpha=0.3)\n", "\n", "# 解决方案2：添加随机抖动\n", "x_jitter = x_overlap + np.random.normal(0, 0.05, len(x_overlap))\n", "y_jitter = y_overlap + np.random.normal(0, 0.05, len(y_overlap))\n", "axes[2].scatter(x_jitter, y_jitter, s=30, alpha=0.5)\n", "axes[2].set_title('解决方案2：添加抖动')\n", "axes[2].grid(True, alpha=0.3)\n", "\n", "plt.tight_layout()\n", "plt.show()\n", "\n", "print(\"💡 解决重叠的方法：\")\n", "print(\"1. 降低透明度 (alpha=0.1-0.5)\")\n", "print(\"2. 减小点的大小 (s=10-30)\")\n", "print(\"3. 添加轻微的随机抖动\")\n", "print(\"4. 考虑使用六边形分箱图 (hex<PERSON>)\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["**预防建议**：在绘制大数据集之前，先查看数据分布，选择合适的可视化策略"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 问题2：颜色映射不清晰或颜色条缺失\n", "**错误现象**：使用数值数组作为颜色时，颜色变化不明显或缺少颜色条说明\n", "**原因分析**：颜色映射范围设置不当，或忘记添加颜色条\n", "**解决方案**："]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 解决颜色映射问题\n", "np.random.seed(42)\n", "x = np.random.randn(100)\n", "y = np.random.randn(100)\n", "colors = x + y  # 颜色值\n", "\n", "fig, axes = plt.subplots(1, 2, figsize=(12, 5))\n", "fig.suptitle('颜色映射优化', fontsize=14, fontweight='bold')\n", "\n", "# 问题示例：颜色映射不清晰\n", "scatter1 = axes[0].scatter(x, y, c=colors, s=50)\n", "axes[0].set_title('问题：缺少颜色条')\n", "axes[0].grid(True, alpha=0.3)\n", "\n", "# 解决方案：添加颜色条和优化颜色映射\n", "scatter2 = axes[1].scatter(x, y, c=colors, s=50, cmap='viridis', \n", "                          vmin=colors.min(), vmax=colors.max())\n", "axes[1].set_title('解决方案：清晰的颜色映射')\n", "axes[1].grid(True, alpha=0.3)\n", "\n", "# 添加颜色条\n", "cbar = plt.colorbar(scatter2, ax=axes[1])\n", "cbar.set_label('颜色值', rotation=270, labelpad=15)\n", "\n", "plt.tight_layout()\n", "plt.show()\n", "\n", "print(\"🎨 颜色映射最佳实践：\")\n", "print(\"1. 选择合适的颜色映射 (cmap='viridis', 'plasma', 'coolwarm')\")\n", "print(\"2. 设置明确的颜色范围 (vmin, vmax)\")\n", "print(\"3. 添加颜色条说明\")\n", "print(\"4. 考虑色盲友好的颜色方案\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 问题3：图例过于复杂或位置不当\n", "**错误现象**：多个图例重叠，或图例遮挡了重要的数据点\n", "**解决方案**："]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 解决图例问题\n", "np.random.seed(42)\n", "categories = ['A', 'B', 'C', 'D', 'E']\n", "colors = ['#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7']\n", "\n", "plt.figure(figsize=(12, 8))\n", "\n", "for i, (cat, color) in enumerate(zip(categories, colors)):\n", "    x = np.random.normal(i, 0.5, 30)\n", "    y = np.random.normal(i, 0.5, 30)\n", "    plt.scatter(x, y, c=color, s=60, alpha=0.7, label=f'类别{cat}')\n", "\n", "plt.title('优化图例显示', fontsize=14, fontweight='bold')\n", "plt.xlabel('X变量')\n", "plt.ylabel('Y变量')\n", "\n", "# 优化图例：放在图外\n", "plt.legend(bbox_to_anchor=(1.05, 1), loc='upper left', \n", "          frameon=True, fancybox=True, shadow=True)\n", "plt.grid(True, alpha=0.3)\n", "plt.tight_layout()\n", "plt.show()\n", "\n", "print(\"📋 图例优化技巧：\")\n", "print(\"1. 使用 bbox_to_anchor 将图例放在图外\")\n", "print(\"2. 选择合适的 loc 参数\")\n", "print(\"3. 使用 ncol 参数创建多列图例\")\n", "print(\"4. 通过 frameon, fancybox, shadow 美化图例\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 📚 知识小结\n", "\n", "### 核心要点\n", "- **基本语法**：`plt.scatter(x, y, s=size, c=color, marker=shape, alpha=transparency)`\n", "- **多维编码**：位置(x,y) + 大小(s) + 颜色(c) + 形状(marker) 可同时展示4个维度\n", "- **颜色映射**：使用cmap参数和colorbar()创建连续颜色映射\n", "- **分类展示**：通过循环为不同类别使用不同颜色和形状\n", "- **数据预处理**：注意处理异常值、确保数据范围合理\n", "\n", "### 最佳实践\n", "- **数据量控制**：超过1000个点时考虑透明度和抖动\n", "- **颜色选择**：使用色盲友好的颜色方案\n", "- **图例管理**：合理安排图例位置，避免遮挡数据\n", "- **统计信息**：适当添加趋势线和相关系数\n", "\n", "### 下节预告\n", "下一节我们将学习**柱状图（条形图）**的详细用法，包括：\n", "- 垂直和水平柱状图\n", "- 分组和堆叠柱状图\n", "- 柱状图的美化和定制\n", "- 柱状图与其他图表的组合使用\n", "\n", "## 🌟 扩展阅读\n", "- [Matplotlib散点图官方文档](https://matplotlib.org/stable/api/_as_gen/matplotlib.pyplot.scatter.html)\n", "- [颜色映射参考](https://matplotlib.org/stable/tutorials/colors/colormaps.html)\n", "- [标记样式参考](https://matplotlib.org/stable/api/markers_api.html)\n", "- [统计可视化最佳实践](https://matplotlib.org/stable/tutorials/introductory/sample_plots.html)"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.5"}}, "nbformat": 4, "nbformat_minor": 4}